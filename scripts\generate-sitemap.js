#!/usr/bin/env node

/**
 * Sitemap generator for MBI website
 * Run with: node scripts/generate-sitemap.js
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const DOMAIN = 'https://site.millennialbusinessinnovations.com'

// Static pages
const staticPages = [
  {
    url: '/',
    changefreq: 'weekly',
    priority: '1.0',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/portfolio',
    changefreq: 'monthly',
    priority: '0.8',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/blog',
    changefreq: 'daily',
    priority: '0.9',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/privacy-policy',
    changefreq: 'yearly',
    priority: '0.3',
    lastmod: new Date().toISOString().split('T')[0]
  },
  {
    url: '/terms-and-conditions',
    changefreq: 'yearly',
    priority: '0.3',
    lastmod: new Date().toISOString().split('T')[0]
  }
]

// Generate XML sitemap
function generateSitemap() {
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
${staticPages.map(page => `  <url>
    <loc>${DOMAIN}${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`).join('\n')}
</urlset>`

  // Write sitemap to public directory
  const sitemapPath = path.join(__dirname, '..', 'public', 'sitemap.xml')
  fs.writeFileSync(sitemapPath, xml, 'utf8')
  
  console.log('✅ Sitemap generated successfully at:', sitemapPath)
  console.log(`📄 Generated ${staticPages.length} URLs`)
}

// Generate robots.txt if it doesn't exist
function generateRobotsTxt() {
  const robotsPath = path.join(__dirname, '..', 'public', 'robots.txt')
  
  if (!fs.existsSync(robotsPath)) {
    const robotsContent = `User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/

Sitemap: ${DOMAIN}/sitemap.xml`
    
    fs.writeFileSync(robotsPath, robotsContent, 'utf8')
    console.log('✅ Robots.txt generated successfully')
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  try {
    generateSitemap()
    generateRobotsTxt()
    console.log('🚀 SEO files generated successfully!')
  } catch (error) {
    console.error('❌ Error generating SEO files:', error)
    process.exit(1)
  }
}

export { generateSitemap, generateRobotsTxt }
