-- Fix workflow trigger function to use correct URL and auth
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION trigger_workflows_on_quote()
RETURNS TRIGGER AS $$
DECLARE
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id UUID;
BEGIN
    -- Create trigger data from the new quote
    trigger_data := jsonb_build_object(
        'quote', row_to_json(NEW),
        'contact', jsonb_build_object(
            'id', NEW.id,
            'name', NEW.name,
            'email', NEW.email,
            'company', NEW.company,
            'phone', NEW.phone
        ),
        'event', 'quote.created',
        'timestamp', NOW()
    );

    -- Find workflows that should be triggered by quote creation
    FOR workflow_record IN
        SELECT w.id, w.name, w.nodes
        FROM workflows w
        WHERE w.active = true
        AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(w.nodes) AS node
            WHERE node->>'type' = 'trigger'
            AND node->'data'->>'triggerType' = 'quote.created'
        )
    LOOP
        -- Log workflow execution as pending
        INSERT INTO workflow_executions (
            workflow_id,
            workflow_name,
            trigger_data,
            status
        ) VALUES (
            workflow_record.id,
            workflow_record.name,
            trigger_data,
            'pending'
        ) RETURNING id INTO execution_id;

        -- Call the workflow execution function asynchronously
        -- Using hardcoded URL and service role key for reliability
        PERFORM net.http_post(
            url := 'https://bwdtlhwdmhqmhxch.supabase.co/functions/v1/execute-workflow',
            headers := jsonb_build_object(
                'Content-Type', 'application/json',
                'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ3ZHRsaHdkbWhxbWh4Y2giLCJyb2xlIjoic2VydmljZV9yb2xlIiwiaWF0IjoxNzM0NjkxMjU0LCJleHAiOjIwNTAyNjcyNTR9.pJEcHU2jzv2ctDTb1IIItmJaVKKfCTlpXCVTg'
            ),
            body := jsonb_build_object(
                'workflow_id', workflow_record.id,
                'execution_id', execution_id,
                'trigger_data', trigger_data
            )
        );
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
