import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Quote, ArrowRight, Star, Plus, Settings } from 'lucide-react'
import { Link } from 'react-router-dom'
import { getTestimonials, BlogPost, getDisplayAuthor } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

const TestimonialSection = () => {
  const [testimonials, setTestimonials] = useState<BlogPost[]>([])
  const [featuredTestimonial, setFeaturedTestimonial] = useState<BlogPost | null>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [loading, setLoading] = useState(true)
  const { user, profile } = useAuth()

  const canCreateTestimonial = user && profile
  const isAdmin = profile && ['owner', 'super_admin', 'admin'].includes(profile.role)

  useEffect(() => {
    fetchTestimonials()
  }, [])

  const fetchTestimonials = async () => {
    try {
      const { data, error } = await getTestimonials()

      if (error) {
        console.error('Error fetching testimonials:', error)
        return
      }

      if (data && data.length > 0) {
        setTestimonials(data)

        // Look for featured testimonials first
        const featured = data.find(post => post.is_featured)
        if (featured) {
          setFeaturedTestimonial(featured)
        } else {
          // Fallback to the first testimonial if none are featured
          setFeaturedTestimonial(data[0])
        }
      }
    } catch (error) {
      console.error('Error fetching testimonials:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <section className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-50 via-blue-50/30 to-cyan-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
              <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          </div>
        </div>
      </section>
    )
  }

  if (!featuredTestimonial) {
    return null
  }

  // Extract testimonial data from blog post content
  const extractTestimonialData = (post: BlogPost) => {
    const content = typeof post.content === 'string' ? post.content : JSON.stringify(post.content)

    // Extract company info and project type
    // Look for HTML format: <h2>About [Company]</h2> followed by <p><strong>Project Type:</strong> [Type]</p>
    const companyMatch = content.match(/<h2>About\s+([^<]+)<\/h2>[\s\S]*?<p><strong>Project Type:<\/strong>\s*([^<]+)<\/p>/i)
    const projectType = companyMatch ? companyMatch[2].trim() : 'SaaS Platform'

    // Extract rating - look for star emojis and rating text
    const ratingMatch = content.match(/⭐{1,5}.*?(\d+)\/5 stars/i) || content.match(/(\d+)\/5 stars/i)
    const rating = ratingMatch ? parseInt(ratingMatch[1]) : 5

    // Extract main testimonial text from "My Experience with MBI" section
    // Look for HTML format: <h2>My Experience with MBI</h2> followed by <p>content</p>
    const experienceMatch = content.match(/<h2>My Experience with MBI<\/h2>\s*<p>([^<]+)/i)
    let quote = 'Great experience working with MBI!'

    if (experienceMatch) {
      quote = experienceMatch[1].trim()
    } else {
      // Fallback: try to extract from post excerpt if available
      if (post.excerpt) {
        quote = post.excerpt
      }
    }

    // Extract results from "Results & Impact" section
    const resultsMatch = content.match(/<h2>Results &amp; Impact<\/h2>\s*<p>([^<]+)/i)
    const results = resultsMatch ? resultsMatch[1].trim() : null

    return { projectType, rating, quote, results }
  }

  const testimonialData = extractTestimonialData(featuredTestimonial)
  const displayAuthor = getDisplayAuthor(featuredTestimonial)

  return (
    <section className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-gray-50 via-blue-50/30 to-cyan-50/30 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-r from-blue-300/10 to-cyan-300/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/10 to-teal-300/10 rounded-full blur-xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          {/* Section Header */}
          <div className="mb-8 sm:mb-12">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="flex">
                {[...Array(testimonialData.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                Trusted by 50+ Startups
              </span>
            </div>
            <div className="flex items-center justify-center gap-4 mb-4">
              <h2 className="font-montserrat font-bold text-2xl sm:text-3xl md:text-4xl text-gray-900 dark:text-white">
                Success Stories That <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Speak Volumes</span>
              </h2>
              {isAdmin && (
                <Link to="/admin/blog?filter=testimonials">
                  <Button variant="ghost" size="sm" className="text-gray-500 hover:text-primary">
                    <Settings className="w-4 h-4" />
                  </Button>
                </Link>
              )}
            </div>
          </div>

          {/* Main Testimonial Card */}
          <Card 
            className="bg-white/80 dark:bg-white/5 border border-white/40 dark:border-white/10 backdrop-blur-lg shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] cursor-pointer"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <CardContent className="p-6 sm:p-8 md:p-10">
              {/* Quote Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                  <Quote className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* Testimonial Quote */}
              <blockquote className="text-lg sm:text-xl md:text-2xl text-gray-700 dark:text-gray-200 font-poppins leading-relaxed mb-8 italic">
                "{testimonialData.quote}"
              </blockquote>

              {/* Author Info */}
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                <div className="text-center sm:text-left">
                  <div className="font-montserrat font-semibold text-lg text-gray-900 dark:text-white">
                    {displayAuthor.name}
                  </div>
                  <div className="font-poppins text-gray-600 dark:text-gray-400">
                    {featuredTestimonial.title}
                  </div>
                  <div className="font-poppins text-sm text-primary font-medium">
                    {testimonialData.projectType}
                  </div>
                </div>

                {/* Results */}
                <div className="flex flex-col sm:flex-row gap-4 text-center">
                  {testimonialData.results && (
                    <div className="bg-green-50 dark:bg-green-900/20 px-4 py-2 rounded-lg border border-green-200 dark:border-green-800">
                      <div className="font-montserrat font-bold text-green-700 dark:text-green-400 text-sm">
                        {testimonialData.results}
                      </div>
                    </div>
                  )}
                  <div className="bg-blue-50 dark:bg-blue-900/20 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="font-montserrat font-bold text-blue-700 dark:text-blue-400 text-sm">
                      Success Story
                    </div>
                  </div>
                </div>
              </div>

              {/* Hover CTA */}
              <div className={`mt-6 transition-all duration-300 ${isHovered ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform translate-y-2'}`}>
                <div className="flex gap-3 justify-center">
                  <Link to={`/blog/${featuredTestimonial.slug}`}>
                    <Button
                      variant="ghost"
                      className="text-primary hover:text-primary/80 hover:bg-primary/10 font-poppins group"
                    >
                      Read Full Story
                      <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                  <Link to="/testimonials">
                    <Button
                      variant="ghost"
                      className="text-gray-600 hover:text-primary hover:bg-primary/10 font-poppins group"
                    >
                      More Success Stories
                      <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bottom CTA */}
          <div className="mt-8 sm:mt-12">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link to="/testimonials">
                <Button
                  variant="outline"
                  className="border-primary text-primary hover:bg-primary/10 hover:border-primary/50 font-poppins px-6 py-2"
                >
                  View All Success Stories
                </Button>
              </Link>

              {canCreateTestimonial && (
                <Link to="/admin/blog/testimonial">
                  <Button
                    className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 py-2"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Share Your Story
                  </Button>
                </Link>
              )}
            </div>

            {!user && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
                <Link to="/blog" className="text-primary hover:underline">Join our blog community</Link> to share your success story with MBI
              </p>
            )}
          </div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
