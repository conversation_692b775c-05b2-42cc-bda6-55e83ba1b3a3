import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import AdminLayout from '@/components/layout/AdminLayout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  Eye,
  MousePointer,
  Clock,
  TrendingUp,
  Users,
  Globe,
  Smartphone,
  Monitor,
  Tablet,
  ExternalLink,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react'
import { toast } from 'sonner'
import { supabase } from '@/lib/supabase'

interface BlogAnalytics {
  post_id: string
  title: string
  slug: string
  view_count: number
  unique_view_count: number
  click_count: number
  avg_reading_time: number
  bounce_rate: number
  reaction_count: number
  comment_count: number
  share_count: number
  published_at: string
}

interface OverviewStats {
  totalViews: number
  totalUniqueViews: number
  totalClicks: number
  avgReadingTime: number
  avgBounceRate: number
  totalPosts: number
  publishedPosts: number
}

interface TrafficSource {
  referrer: string
  views: number
  percentage: number
}

interface DeviceStats {
  device_type: string
  views: number
  percentage: number
}

const Analytics = () => {
  const { profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const [analytics, setAnalytics] = useState<BlogAnalytics[]>([])
  const [overviewStats, setOverviewStats] = useState<OverviewStats>({
    totalViews: 0,
    totalUniqueViews: 0,
    totalClicks: 0,
    avgReadingTime: 0,
    avgBounceRate: 0,
    totalPosts: 0,
    publishedPosts: 0
  })
  const [trafficSources, setTrafficSources] = useState<TrafficSource[]>([])
  const [deviceStats, setDeviceStats] = useState<DeviceStats[]>([])
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')
  const [sortBy, setSortBy] = useState('view_count')

  useEffect(() => {
    loadAnalytics()
  }, [currentOrganization, timeRange])

  const loadAnalytics = async () => {
    if (!currentOrganization) return

    setLoading(true)
    try {
      // Calculate date range
      const endDate = new Date()
      const startDate = new Date()
      switch (timeRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      // Determine organization filter based on user role
      const orgFilter = (profile?.role === 'saas_owner' || profile?.role === 'super_admin') 
        ? {} 
        : { organization_id: currentOrganization.id }

      // Load blog post analytics
      const { data: blogData, error: blogError } = await supabase
        .from('blog_posts')
        .select(`
          id,
          title,
          slug,
          view_count,
          unique_view_count,
          click_count,
          avg_reading_time,
          bounce_rate,
          reaction_count,
          comment_count,
          share_count,
          published_at
        `)
        .match(orgFilter)
        .eq('status', 'published')
        .gte('published_at', startDate.toISOString())
        .order(sortBy, { ascending: false })

      if (blogError) throw blogError

      setAnalytics(blogData || [])

      // Calculate overview stats
      const stats = (blogData || []).reduce((acc, post) => ({
        totalViews: acc.totalViews + (post.view_count || 0),
        totalUniqueViews: acc.totalUniqueViews + (post.unique_view_count || 0),
        totalClicks: acc.totalClicks + (post.click_count || 0),
        avgReadingTime: acc.avgReadingTime + (post.avg_reading_time || 0),
        avgBounceRate: acc.avgBounceRate + (post.bounce_rate || 0),
        totalPosts: acc.totalPosts + 1,
        publishedPosts: acc.publishedPosts + 1
      }), {
        totalViews: 0,
        totalUniqueViews: 0,
        totalClicks: 0,
        avgReadingTime: 0,
        avgBounceRate: 0,
        totalPosts: 0,
        publishedPosts: 0
      })

      if (stats.totalPosts > 0) {
        stats.avgReadingTime = Math.round(stats.avgReadingTime / stats.totalPosts)
        stats.avgBounceRate = Math.round((stats.avgBounceRate / stats.totalPosts) * 100) / 100
      }

      setOverviewStats(stats)

      // Load traffic sources
      const { data: trafficData, error: trafficError } = await supabase
        .from('blog_post_views')
        .select('referrer')
        .gte('created_at', startDate.toISOString())
        .not('referrer', 'is', null)

      if (!trafficError && trafficData) {
        const referrerCounts = trafficData.reduce((acc: Record<string, number>, view) => {
          const referrer = view.referrer || 'Direct'
          acc[referrer] = (acc[referrer] || 0) + 1
          return acc
        }, {})

        const totalTraffic = Object.values(referrerCounts).reduce((sum: number, count) => sum + count, 0)
        const sources = Object.entries(referrerCounts)
          .map(([referrer, views]) => ({
            referrer: referrer.replace(/^https?:\/\//, '').split('/')[0] || 'Direct',
            views: views as number,
            percentage: Math.round((views as number / totalTraffic) * 100)
          }))
          .sort((a, b) => b.views - a.views)
          .slice(0, 5)

        setTrafficSources(sources)
      }

      // Load device stats
      const { data: deviceData, error: deviceError } = await supabase
        .from('blog_post_views')
        .select('device_type')
        .gte('created_at', startDate.toISOString())
        .not('device_type', 'is', null)

      if (!deviceError && deviceData) {
        const deviceCounts = deviceData.reduce((acc: Record<string, number>, view) => {
          const device = view.device_type || 'Unknown'
          acc[device] = (acc[device] || 0) + 1
          return acc
        }, {})

        const totalDeviceViews = Object.values(deviceCounts).reduce((sum: number, count) => sum + count, 0)
        const devices = Object.entries(deviceCounts)
          .map(([device_type, views]) => ({
            device_type,
            views: views as number,
            percentage: Math.round((views as number / totalDeviceViews) * 100)
          }))
          .sort((a, b) => b.views - a.views)

        setDeviceStats(devices)
      }

    } catch (error) {
      console.error('Error loading analytics:', error)
      toast.error('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile': return Smartphone
      case 'tablet': return Tablet
      case 'desktop': return Monitor
      default: return Monitor
    }
  }

  const getTrendIcon = (value: number, threshold: number = 0) => {
    if (value > threshold) return <ArrowUp className="h-4 w-4 text-green-500" />
    if (value < threshold) return <ArrowDown className="h-4 w-4 text-red-500" />
    return <Minus className="h-4 w-4 text-gray-500" />
  }

  return (
    <ProtectedRoute requiredRole={['user', 'admin', 'super_admin', 'owner', 'saas_owner']}>
      <AdminLayout
        title="Blog Analytics"
        subtitle="Track your blog performance and audience engagement"
        actions={
          <div className="flex gap-2">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={loadAnalytics} variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        }
      >
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Overview Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overviewStats.totalViews.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    {overviewStats.totalUniqueViews.toLocaleString()} unique visitors
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
                  <MousePointer className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overviewStats.totalClicks.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    {overviewStats.totalViews > 0 
                      ? `${Math.round((overviewStats.totalClicks / overviewStats.totalViews) * 100)}% CTR`
                      : '0% CTR'
                    }
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Reading Time</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatDuration(overviewStats.avgReadingTime)}</div>
                  <p className="text-xs text-muted-foreground">
                    {overviewStats.avgBounceRate}% bounce rate
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Published Posts</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{overviewStats.publishedPosts}</div>
                  <p className="text-xs text-muted-foreground">
                    in selected period
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Top Performing Posts */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Top Performing Posts</CardTitle>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-40">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view_count">Views</SelectItem>
                        <SelectItem value="unique_view_count">Unique Views</SelectItem>
                        <SelectItem value="click_count">Clicks</SelectItem>
                        <SelectItem value="avg_reading_time">Reading Time</SelectItem>
                        <SelectItem value="reaction_count">Reactions</SelectItem>
                      </SelectContent>
                    </Select>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {analytics.slice(0, 10).map((post) => (
                        <div key={post.post_id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium truncate">{post.title}</h4>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                              <span className="flex items-center gap-1">
                                <Eye className="h-3 w-3" />
                                {post.view_count?.toLocaleString() || 0}
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="h-3 w-3" />
                                {post.unique_view_count?.toLocaleString() || 0}
                              </span>
                              <span className="flex items-center gap-1">
                                <MousePointer className="h-3 w-3" />
                                {post.click_count?.toLocaleString() || 0}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {formatDuration(post.avg_reading_time || 0)}
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={post.bounce_rate > 70 ? "destructive" : post.bounce_rate > 50 ? "secondary" : "default"}>
                              {post.bounce_rate?.toFixed(1) || 0}% bounce
                            </Badge>
                            <Button variant="ghost" size="sm" asChild>
                              <a href={`/blog/${post.slug}`} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4" />
                              </a>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Traffic Sources & Device Stats */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Traffic Sources
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {trafficSources.map((source, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm truncate flex-1">{source.referrer}</span>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{source.views}</span>
                            <Badge variant="outline">{source.percentage}%</Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Monitor className="h-4 w-4" />
                      Device Types
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {deviceStats.map((device, index) => {
                        const DeviceIcon = getDeviceIcon(device.device_type)
                        return (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <DeviceIcon className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm capitalize">{device.device_type}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">{device.views}</span>
                              <Badge variant="outline">{device.percentage}%</Badge>
                            </div>
                          </div>
                        )
                      })}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </ProtectedRoute>
  )
}

export default Analytics
