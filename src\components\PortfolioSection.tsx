
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const PortfolioSection = () => {
  const navigate = useNavigate();

  const featuredProjects = [
    {
      title: 'Aha-Innovations Platform',
      category: 'SaaS Platform',
      description: 'All-in-one business innovation platform featuring project management, collaboration tools, and analytics dashboard.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://site.aha-innovations.com/',
      tech: ['React', 'Next.js', 'TypeScript'],
      metrics: 'Live Platform',
      url: 'https://site.aha-innovations.com/'
    },
    {
      title: 'RR Twins',
      category: 'Custom Web Solution',
      description: 'Professional twin performers website showcasing their entertainment services, portfolio, and booking system.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://rrtwins.com/',
      tech: ['React', 'Tailwind CSS', 'Vercel'],
      metrics: 'Entertainment Portfolio',
      url: 'https://rrtwins.com/'
    },
    {
      title: 'Denti-Nexus Clinic Suite',
      category: 'Healthcare Platform',
      description: 'Comprehensive dental clinic management system with appointment scheduling, patient records, and billing integration.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://denti-nexus-clinic-suite.vercel.app/',
      tech: ['React', 'Next.js', 'Healthcare APIs'],
      metrics: 'Healthcare Solution',
      url: 'https://denti-nexus-clinic-suite.vercel.app/'
    }
  ];

  return (
    <section id="portfolio" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-cyan-50 via-blue-50 to-teal-50 dark:from-black dark:via-cyan-900/10 dark:to-blue-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-60 h-60 bg-gradient-to-r from-blue-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-teal-300/10 to-cyan-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Our <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Portfolio</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            Discover some of our recent projects and the impact we've made for our clients.
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {featuredProjects.map((project, index) => (
            <Card
              key={index}
              className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg hover:bg-white dark:hover:bg-white/10 transition-all duration-300 overflow-hidden group hover:transform hover:scale-105"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-40 sm:h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
                  <span className="bg-primary/80 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-poppins">
                    {project.category}
                  </span>
                </div>
              </div>

              <CardContent className="p-4 sm:p-6 space-y-3 sm:space-y-4">
                <h3 className="font-montserrat font-semibold text-lg sm:text-xl text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                  {project.title}
                </h3>
                <p className="font-poppins text-gray-600 dark:text-gray-300 text-sm">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech, idx) => (
                    <span
                      key={idx}
                      className="bg-accent/20 text-accent px-2 py-1 rounded text-xs font-poppins"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-3 sm:pt-4 border-t border-gray-200 dark:border-white/10">
                  <span className="text-secondary text-xs sm:text-sm font-poppins font-semibold">
                    {project.metrics}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-primary hover:text-primary/80 hover:bg-primary/10 text-xs sm:text-sm"
                    onClick={() => window.open(project.url, '_blank')}
                  >
                    Visit Site
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-8 sm:mt-12">
          <Button
            onClick={() => navigate('/portfolio')}
            className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 sm:px-8 py-2 sm:py-3 rounded-full text-sm sm:text-base"
          >
            View All Projects
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
