import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { But<PERSON> } from '@/components/ui/button'
import { Settings, Edit, Plus, BarChart3 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

const AdminQuickAccess = () => {
  const { user, profile } = useAuth()

  // Only show if user is authenticated and has at least user role
  if (!user || !profile || !['super_admin', 'admin', 'user', 'owner', 'saas_owner'].includes(profile.role)) {
    return null
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size="lg"
            className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 bg-primary hover:bg-primary/90 text-white border-2 border-white/20 backdrop-blur-sm"
            title="Admin Quick Access"
          >
            <Settings className="h-6 w-6" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="end" 
          className="w-56 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-white/30 dark:border-gray-700/50"
        >
          <div className="px-3 py-2 text-sm font-medium text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700">
            Your Blog
          </div>
          
          <DropdownMenuItem asChild>
            <Link to="/admin/blog" className="flex items-center gap-2 cursor-pointer">
              <BarChart3 className="h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuItem asChild>
            <Link to="/admin/blog/new" className="flex items-center gap-2 cursor-pointer">
              <Plus className="h-4 w-4" />
              New Post
            </Link>
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem asChild>
            <Link to="/admin/blog/profile" className="flex items-center gap-2 cursor-pointer">
              <Edit className="h-4 w-4" />
              Profile Settings
            </Link>
          </DropdownMenuItem>
          
          {/* Show admin-only options for admin+ roles */}
          {['super_admin', 'admin'].includes(profile.role) && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link to="/admin/blog/users" className="flex items-center gap-2 cursor-pointer">
                  <Settings className="h-4 w-4" />
                  User Management
                </Link>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

export default AdminQuickAccess
