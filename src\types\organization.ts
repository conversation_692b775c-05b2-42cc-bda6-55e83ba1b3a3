// Organization and Multi-Tenant Types

export type SubscriptionPlan = 'free' | 'basic' | 'pro' | 'enterprise'
export type SubscriptionStatus = 'active' | 'cancelled' | 'past_due' | 'trialing'
export type OrganizationRole = 'owner' | 'admin' | 'editor' | 'user' | 'viewer'
export type PlatformRole = 'saas_owner' | 'super_admin' | 'admin' | 'editor' | 'user' | 'viewer'

export interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  avatar_url?: string
  subscription_plan: SubscriptionPlan
  subscription_status: SubscriptionStatus
  workflow_credits_used: number
  workflow_credits_limit: number
  credits_reset_date: string
  stripe_customer_id?: string
  stripe_subscription_id?: string
  created_at: string
  updated_at: string
}

export interface OrganizationMember {
  id: string
  organization_id: string
  user_id: string
  role: OrganizationRole
  invited_by?: string
  invited_at: string
  joined_at?: string
  is_active: boolean
  organization?: Organization
  user?: {
    id: string
    full_name?: string
    email: string
    avatar_url?: string
  }
}

export interface WorkspaceInvitation {
  id: string
  organization_id: string
  email: string
  role: OrganizationRole
  invited_by: string
  token: string
  expires_at: string
  accepted_at?: string
  created_at: string
  organization?: Organization
  inviter?: {
    full_name?: string
    email: string
  }
}

export interface WorkflowCreditUsage {
  id: string
  organization_id: string
  workflow_id?: string
  action_type: string
  credits_used: number
  execution_id?: string
  metadata?: Record<string, any>
  created_at: string
}

export interface AiCreditUsage {
  id: string
  organization_id: string
  user_id: string
  request_type: 'blog_generation' | 'chat_help' | 'workflow_help' | 'content_analysis'
  model_used: string
  tokens_used: number
  credits_used: number
  prompt_tokens: number
  completion_tokens: number
  created_at: string
}

export interface SupportAccessLog {
  id: string
  support_user_id: string
  target_organization_id: string
  target_user_id?: string
  access_reason: string
  access_granted_by?: string
  started_at: string
  ended_at?: string
  is_active: boolean
}

// Subscription Plan Features
export const SUBSCRIPTION_FEATURES = {
  free: {
    name: 'Free',
    price: 0,
    workflowCredits: 100, // Like Zapier's free tier
    aiCredits: 50, // AI requests per month
    maxTeamMembers: 1,
    emailCredits: 50, // Free Gmail integration
    features: [
      'Public blog posting',
      'Social features (comments, likes)',
      'Basic testimonials',
      'Basic workflow automation (100 credits)',
      'AI chat assistance (50 requests)',
      'AI blog generation (basic)',
      'Gmail integration',
      'Community support'
    ],
    limitations: [
      'Limited workflow credits',
      'Limited AI requests',
      'No team collaboration',
      'Basic support only'
    ]
  },
  basic: {
    name: 'Basic',
    price: 10, // Much more affordable!
    workflowCredits: 1000,
    aiCredits: 500, // AI requests per month
    maxTeamMembers: 3,
    emailCredits: 500, // Free Gmail integration
    features: [
      'All Free features',
      'Advanced workflow automation (1,000 credits)',
      'AI chat assistance (500 requests)',
      'AI blog generation (advanced)',
      'AI workflow help',
      'Team collaboration (3 members)',
      'Email automation via Gmail',
      'Basic webhooks',
      'Email support'
    ],
    limitations: [
      'Limited team size',
      'Basic integrations only'
    ]
  },
  pro: {
    name: 'Pro',
    price: 15, // Your suggested price!
    workflowCredits: 5000,
    aiCredits: 2000, // AI requests per month
    maxTeamMembers: 10,
    emailCredits: 2000, // Free Gmail integration
    features: [
      'All Basic features',
      'Advanced workflows (5,000 credits)',
      'AI chat assistance (2,000 requests)',
      'AI content analysis',
      'AI workflow optimization',
      'Advanced webhook integrations',
      'Analytics dashboard',
      'Team collaboration (10 members)',
      'Priority email support',
      'Custom integrations'
    ],
    limitations: [
      'Standard support response time'
    ]
  },
  enterprise: {
    name: 'Enterprise',
    price: 49, // Much more reasonable!
    workflowCredits: 25000,
    aiCredits: -1, // unlimited AI requests
    maxTeamMembers: -1, // unlimited
    emailCredits: -1, // unlimited
    features: [
      'All Pro features',
      'Unlimited team members',
      'Unlimited workflow credits (25,000+)',
      'Unlimited AI requests',
      'AI model selection (premium models)',
      'Custom AI training',
      'White-label options',
      'Custom integrations',
      'Dedicated account manager',
      'SLA guarantee',
      'Advanced security features',
      'Priority phone support'
    ],
    limitations: []
  }
} as const

// Workflow Action Credit Costs (Gmail integration saves money!)
export const WORKFLOW_CREDIT_COSTS = {
  // Free actions (no external costs)
  conditional_logic: 0,
  delay: 0,
  data_formatting: 0,
  trigger: 0,

  // Low-cost actions
  gmail_send: 0, // FREE when using user's Gmail!
  webhook_call: 1,
  data_processing: 1,

  // Medium-cost actions (external APIs)
  sms_send: 5, // Higher due to SMS provider costs
  third_party_email: 2, // If they choose premium email service
  external_api_call: 2,

  // High-cost actions
  ai_processing: 3, // AI workflow actions
  file_processing: 2
} as const

// AI Model Configuration and Costs
export const AI_MODELS = {
  deepseek_r1: {
    name: 'DeepSeek R1',
    provider: 'DeepSeek',
    inputCostPer1M: 0.14,
    outputCostPer1M: 0.28,
    quality: 'excellent',
    speed: 'fast',
    useCases: ['blog_generation', 'content_analysis', 'complex_reasoning'],
    maxTokens: 128000
  },
  gemini_flash: {
    name: 'Gemini 2.5 Flash',
    provider: 'Google',
    inputCostPer1M: 0.075,
    outputCostPer1M: 0.30,
    quality: 'very_good',
    speed: 'very_fast',
    useCases: ['chat_help', 'quick_questions', 'navigation'],
    maxTokens: 1000000
  },
  gpt_4o_mini: {
    name: 'GPT-4o Mini',
    provider: 'OpenAI',
    inputCostPer1M: 0.15,
    outputCostPer1M: 0.60,
    quality: 'very_good',
    speed: 'fast',
    useCases: ['workflow_help', 'code_assistance', 'general_tasks'],
    maxTokens: 128000
  },
  gemini_pro: {
    name: 'Gemini 2.5 Pro',
    provider: 'Google',
    inputCostPer1M: 3.50,
    outputCostPer1M: 10.50,
    quality: 'excellent',
    speed: 'medium',
    useCases: ['complex_analysis', 'enterprise_tasks', 'premium_content'],
    maxTokens: 1000000
  }
} as const

// AI Credit Costs (per request type)
export const AI_CREDIT_COSTS = {
  // Quick interactions (Gemini Flash)
  chat_help: 1,           // ~$0.10 per 1000 words
  navigation_help: 1,     // Quick questions
  simple_questions: 1,    // Basic assistance

  // Medium interactions (GPT-4o Mini)
  workflow_help: 2,       // ~$0.30 per 1000 words
  code_assistance: 2,     // Technical help
  content_editing: 2,     // Text improvements

  // Heavy interactions (DeepSeek R1)
  blog_generation: 3,     // ~$0.20 per 1000 words (great value!)
  story_creation: 3,      // Long-form content
  complex_reasoning: 3,   // Advanced analysis

  // Premium interactions (Gemini Pro - Enterprise only)
  enterprise_analysis: 10, // ~$5.00 per 1000 words
  custom_training: 15,     // AI model fine-tuning
  premium_content: 10      // High-quality analysis
} as const

// Permission Helpers
export const canAccessWorkflows = (plan: SubscriptionPlan): boolean => {
  // All plans can access workflows, but with different credit limits
  return true
}

export const canInviteMembers = (
  role: OrganizationRole,
  currentMemberCount: number,
  plan: SubscriptionPlan
): boolean => {
  if (!['owner', 'admin', 'saas_owner'].includes(role)) return false

  const maxMembers = SUBSCRIPTION_FEATURES[plan].maxTeamMembers
  if (maxMembers === -1) return true // unlimited

  return currentMemberCount < maxMembers
}

export const canManageOrganization = (role: OrganizationRole): boolean => {
  return ['owner', 'admin', 'saas_owner'].includes(role)
}

export const canManageWorkflows = (role: OrganizationRole, plan: SubscriptionPlan): boolean => {
  return ['user', 'owner', 'admin', 'editor', 'saas_owner'].includes(role) && canAccessWorkflows(plan)
}

export const canViewAnalytics = (role: OrganizationRole, plan: SubscriptionPlan): boolean => {
  // All workspace owners and above can view analytics for their workspace
  return ['user', 'owner', 'admin', 'saas_owner'].includes(role)
}

// Credit Management Helpers
export const getRemainingCredits = (org: Organization): number => {
  return Math.max(0, org.workflow_credits_limit - org.workflow_credits_used)
}

export const getCreditUsagePercentage = (org: Organization): number => {
  if (org.workflow_credits_limit === 0) return 0
  return (org.workflow_credits_used / org.workflow_credits_limit) * 100
}

export const canExecuteWorkflow = (
  org: Organization, 
  estimatedCredits: number
): boolean => {
  if (!canAccessWorkflows(org.subscription_plan)) return false
  return getRemainingCredits(org) >= estimatedCredits
}

// Role Hierarchy (higher number = more permissions)
export const ROLE_HIERARCHY = {
  viewer: 1,
  user: 2, // Free tier users with workflow and email access
  editor: 3,
  admin: 4,
  owner: 5, // Organization owner
  super_admin: 6, // Can manage multiple organizations
  saas_owner: 7 // Highest level - can see all posts and manage everything
} as const

export const hasHigherRole = (
  userRole: OrganizationRole | PlatformRole, 
  targetRole: OrganizationRole | PlatformRole
): boolean => {
  return ROLE_HIERARCHY[userRole as keyof typeof ROLE_HIERARCHY] > 
         ROLE_HIERARCHY[targetRole as keyof typeof ROLE_HIERARCHY]
}

// Organization Context Type for React Context
export interface OrganizationContextType {
  currentOrganization: Organization | null
  userRole: OrganizationRole | null
  organizations: Organization[]
  isLoading: boolean
  switchOrganization: (orgId: string) => Promise<void>
  refreshOrganization: () => Promise<void>
  canAccess: (permission: string) => boolean
}

// API Response Types
export interface CreateOrganizationRequest {
  name: string
  slug: string
  description?: string
  subscription_plan?: SubscriptionPlan
}

export interface InviteMemberRequest {
  email: string
  role: OrganizationRole
  organization_id: string
}

export interface UpdateMemberRoleRequest {
  member_id: string
  new_role: OrganizationRole
}

export interface GrantSupportAccessRequest {
  target_organization_id: string
  target_user_id?: string
  access_reason: string
  duration_hours?: number
}
