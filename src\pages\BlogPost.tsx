import React, { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import BlogMetaTags from '@/components/BlogMetaTags'
import AdminQuickAccess from '@/components/AdminQuickAccess'
import { getBlogPost, BlogPost as BlogPostType, getDisplayAuthor } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ArrowLeft, Calendar, User, Share2, BarChart3 } from 'lucide-react'
import { toast } from 'sonner'
import BlogEngagement from '@/components/BlogEngagement'
import BlogComments from '@/components/BlogComments'

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>()
  const { user, profile } = useAuth()
  const [post, setPost] = useState<BlogPostType | null>(null)
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)

  useEffect(() => {
    const fetchPost = async () => {
      if (!slug) {
        setNotFound(true)
        setLoading(false)
        return
      }

      try {
        const { data, error } = await getBlogPost(slug)
        if (error || !data) {
          setNotFound(true)
          console.error('Error fetching post:', error)
        } else {
          setPost(data)
        }
      } catch (error) {
        setNotFound(true)
        console.error('Error:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPost()
  }, [slug])

  // Execute JavaScript in custom HTML blocks after content is rendered
  useEffect(() => {
    if (post?.content) {
      // Small delay to ensure DOM is updated
      const timer = setTimeout(() => {
        // Find and execute script tags in custom HTML blocks
        const scriptTags = document.querySelectorAll('.custom-html-embed script')
        scriptTags.forEach((script) => {
          const newScript = document.createElement('script')
          if (script.src) {
            newScript.src = script.src
            newScript.async = true
          } else {
            newScript.textContent = script.textContent
          }
          script.parentNode?.replaceChild(newScript, script)
        })
      }, 100)

      return () => clearTimeout(timer)
    }
  }, [post?.content])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post?.title,
          text: post?.excerpt,
          url: window.location.href,
        })
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link copied to clipboard!')
    }
  }

  const renderContent = (content: any) => {
    console.log('Rendering content:', content, typeof content)
    // If it's HTML string (from rich text editor), render directly
    if (typeof content === 'string') {
      // Check if it looks like HTML
      if (content.includes('<') && content.includes('>')) {
        // Process custom HTML blocks to render them properly
        const processedContent = content.replace(
          /<div[^>]*data-type="custom-html"[^>]*data-content="([^"]*)"[^>]*>.*?<\/div>/gs,
          (match, encodedContent) => {
            // Decode the HTML content
            const decodedContent = encodedContent
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#x27;/g, "'")
              .replace(/&amp;/g, '&');

            return `<div class="custom-html-embed my-6">${decodedContent}</div>`;
          }
        );

        return (
          <div
            className="prose prose-lg max-w-none dark:prose-invert
                       prose-headings:font-montserrat prose-headings:font-bold
                       prose-p:font-poppins prose-p:leading-relaxed
                       prose-li:font-poppins prose-li:leading-relaxed
                       prose-a:text-primary prose-a:no-underline hover:prose-a:underline
                       prose-strong:font-bold prose-em:italic
                       prose-code:bg-gray-100 prose-code:dark:bg-gray-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded
                       prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic
                       prose-ul:list-disc prose-ol:list-decimal
                       prose-img:rounded-lg prose-img:shadow-lg
                       [&_.custom-html-embed]:not-prose [&_.custom-html-embed]:my-6"
            dangerouslySetInnerHTML={{ __html: processedContent }}
          />
        )
      }
      // Otherwise treat as plain text
      return <div className="prose prose-lg max-w-none dark:prose-invert font-poppins"><p>{content}</p></div>
    }

    // Render Tiptap JSON content (legacy support)
    if (content && content.content) {
      return (
        <div className="prose prose-lg max-w-none dark:prose-invert">
          {content.content.map((node: any, index: number) => renderNode(node, index))}
        </div>
      )
    }

    return <p>Content not available</p>
  }

  const renderNode = (node: any, index: number): React.ReactNode => {
    switch (node.type) {
      case 'paragraph':
        return (
          <p key={index} className="mb-4">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </p>
        )
      case 'heading':
        const HeadingTag = `h${node.attrs?.level || 2}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag key={index} className="font-montserrat font-bold mb-4 mt-8">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </HeadingTag>
        )
      case 'text':
        let text = node.text
        if (node.marks) {
          node.marks.forEach((mark: any) => {
            switch (mark.type) {
              case 'bold':
                text = <strong key={index}>{text}</strong>
                break
              case 'italic':
                text = <em key={index}>{text}</em>
                break
              case 'link':
                text = (
                  <a key={index} href={mark.attrs.href} className="text-primary hover:underline" target="_blank" rel="noopener noreferrer">
                    {text}
                  </a>
                )
                break
            }
          })
        }
        return text
      case 'bulletList':
        return (
          <ul key={index} className="list-disc pl-6 mb-4">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </ul>
        )
      case 'orderedList':
        return (
          <ol key={index} className="list-decimal pl-6 mb-4">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </ol>
        )
      case 'listItem':
        return (
          <li key={index} className="mb-2">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </li>
        )
      case 'blockquote':
        return (
          <blockquote key={index} className="border-l-4 border-primary pl-4 italic my-6">
            {node.content?.map((child: any, childIndex: number) => renderNode(child, childIndex))}
          </blockquote>
        )
      case 'codeBlock':
        return (
          <pre key={index} className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-6">
            <code>{node.content?.[0]?.text}</code>
          </pre>
        )
      case 'image':
        return (
          <img
            key={index}
            src={node.attrs?.src}
            alt={node.attrs?.alt || ''}
            className="w-full h-auto rounded-lg my-6"
          />
        )
      default:
        return null
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-20 pb-12">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="max-w-4xl mx-auto">
              <div className="animate-pulse space-y-6">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  if (notFound || !post) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-20 pb-12">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="text-center py-20">
              <h1 className="text-4xl font-montserrat font-bold text-gray-900 dark:text-white mb-4">
                Article Not Found
              </h1>
              <p className="text-gray-600 dark:text-gray-400 font-poppins mb-8">
                The article you're looking for doesn't exist or has been removed.
              </p>
              <div className="flex gap-4 justify-center">
                <Link to="/blog">
                  <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Blog
                  </Button>
                </Link>
                <Link to="/testimonials">
                  <Button className="bg-gradient-to-r from-primary to-accent text-white">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Success Stories
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <BlogMetaTags post={post} />
      <Navigation />

      <article className="pt-20 pb-12">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto">
            {/* Navigation Links */}
            <div className="flex items-center justify-between mb-8">
              {post.category === 'testimonials' || (post.tags && post.tags.includes('testimonial')) ? (
                <Link to="/testimonials" className="inline-flex items-center text-primary hover:text-primary/80 font-poppins">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Success Stories
                </Link>
              ) : (
                <Link to="/blog" className="inline-flex items-center text-primary hover:text-primary/80 font-poppins">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Blog
                </Link>
              )}

              {/* Dashboard Button for Authenticated Users */}
              {user && profile && ['super_admin', 'admin', 'user', 'owner', 'saas_owner'].includes(profile.role) && (
                <Link to="/admin/blog" className="inline-flex items-center text-primary hover:text-primary/80 font-poppins">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Link>
              )}
            </div>

            {/* Article Header */}
            <header className="mb-8">
              <h1 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-6">
                {post.title}
              </h1>
              
              <div className="flex flex-wrap items-center gap-6 text-gray-500 dark:text-gray-400 mb-6">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span className="font-poppins">
                    {formatDate(post.published_at || post.created_at)}
                  </span>
                </div>
                
                {(() => {
                  console.log('Post data for author display:', post)
                  console.log('Post author_display field:', post.author_display)
                  const displayAuthor = getDisplayAuthor(post)
                  console.log('Display author result:', displayAuthor)
                  return (
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={displayAuthor.avatar_url || undefined} alt={displayAuthor.name} />
                        <AvatarFallback className="text-sm">
                          {displayAuthor.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="font-poppins">
                        {displayAuthor.name}
                      </span>
                    </div>
                  )
                })()}
                
                <Button
                  onClick={handleShare}
                  variant="ghost"
                  size="sm"
                  className="text-gray-500 hover:text-primary"
                >
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
              </div>

              {post.excerpt && (
                <p className="text-lg text-gray-600 dark:text-gray-300 font-poppins leading-relaxed">
                  {post.excerpt}
                </p>
              )}
            </header>

            {/* Featured Image or Thumbnail */}
            {(post.featured_image || post.thumbnail_url) && (
              <div className="mb-8">
                <img
                  src={post.featured_image || post.thumbnail_url}
                  alt={post.title}
                  className="w-full h-auto rounded-lg shadow-lg"
                />
              </div>
            )}

            {/* Article Content */}
            <div className="font-poppins text-gray-700 dark:text-gray-300 leading-relaxed mb-12">
              {renderContent(post.content)}
            </div>

            {/* Engagement Section */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-8 mb-12">
              <div className="flex items-center justify-center">
                <BlogEngagement
                  postId={post.id}
                  postTitle={post.title}
                  postUrl={`/blog/${post.slug}`}
                  reactionCount={post.reaction_count || 0}
                  commentCount={post.comment_count || 0}
                  saveCount={post.save_count || 0}
                  shareCount={post.share_count || 0}
                  showCounts={true}
                  size="lg"
                  variant="horizontal"
                />
              </div>
            </div>

            {/* Comments Section */}
            <BlogComments
              postId={post.id}
              commentCount={post.comment_count || 0}
            />
          </div>
        </div>
      </article>

      <Footer />

      {/* Admin Quick Access - Only visible to authenticated users */}
      <AdminQuickAccess />
    </div>
  )
}

export default BlogPost
