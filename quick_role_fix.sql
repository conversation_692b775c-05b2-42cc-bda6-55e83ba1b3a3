-- Quick Role Fix for Free Users
-- Run this in Supabase Dashboard > SQL Editor
-- This enables workflows and email integrations for free users

-- Update all 'viewer' role users to 'user' role (except <PERSON> who should be saas_owner)
-- This gives free users access to workflows and email integrations with limited credits
UPDATE profiles 
SET role = 'user'
WHERE role = 'viewer' 
AND email != '<EMAIL>';

-- Verify the changes
SELECT 
  'User Role Update Summary' as summary,
  role,
  COUNT(*) as user_count
FROM profiles 
GROUP BY role
ORDER BY role;

-- Show updated users
SELECT 
  'Updated Users' as status,
  email,
  role,
  full_name
FROM profiles 
WHERE role = 'user'
ORDER BY email;

SELECT 'Role update completed! Users can now access workflows and email integrations.' as status;
