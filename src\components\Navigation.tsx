
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Menu, X, User, LogOut, Settings, PenTool } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';
import CalendarModal from '@/components/CalendarModal';
import AuthModal from '@/components/auth/AuthModal';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const location = useLocation();
  const { user, profile, signOut } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    // If we're not on the home page, navigate to home first
    if (location.pathname !== '/') {
      window.location.href = `/#${sectionId}`;
      return;
    }

    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMobileMenuOpen(false); // Close mobile menu when navigating
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled ? 'bg-white/20 dark:bg-black/20 backdrop-blur-xl border-b border-white/30 dark:border-white/20 shadow-lg' : 'bg-white/10 dark:bg-black/10 backdrop-blur-md'
    }`}>
      <div className="container mx-auto px-4 sm:px-6 py-3 sm:py-4">
        <div className="flex items-center justify-between">
          <div className="font-montserrat font-bold text-xl sm:text-2xl text-gray-900 dark:text-white">
            <span className="text-primary">M</span>BI
          </div>

          {/* Desktop Navigation - Centered */}
          <div className="hidden md:flex items-center justify-center space-x-8 absolute left-1/2 transform -translate-x-1/2">
            <button
              onClick={() => scrollToSection('home')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Home
            </button>
            <button
              onClick={() => scrollToSection('about')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              About
            </button>
            <button
              onClick={() => scrollToSection('services')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Services
            </button>
            <button
              onClick={() => scrollToSection('portfolio')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Portfolio
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Pricing
            </button>
            <Link
              to="/blog"
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Blog
            </Link>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins"
            >
              Contact
            </button>
          </div>

          {/* Desktop Actions */}
          <div className="hidden sm:flex items-center gap-4">
            <ThemeToggle />

            {user ? (
              // Authenticated user menu
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={profile?.avatar_url} alt={profile?.full_name || user.email} />
                      <AvatarFallback>
                        {profile?.full_name ? profile.full_name.charAt(0).toUpperCase() : user.email?.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{profile?.full_name || 'User'}</p>
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link to="/admin/blog" className="flex items-center">
                      <PenTool className="mr-2 h-4 w-4" />
                      Blog Dashboard
                    </Link>
                  </DropdownMenuItem>
                  {(profile?.role === 'owner' || profile?.role === 'super_admin' || profile?.role === 'admin') && (
                    <DropdownMenuItem asChild>
                      <Link to="/admin/users" className="flex items-center">
                        <Settings className="mr-2 h-4 w-4" />
                        Admin Settings
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={signOut} className="text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              // Sign in button for non-authenticated users
              <Button
                onClick={() => setIsAuthModalOpen(true)}
                variant="outline"
                className="border-primary text-primary hover:bg-primary/10 hover:border-primary/50 font-poppins px-4 py-2 rounded-full transition-all duration-300"
              >
                <User className="mr-2 h-4 w-4" />
                Sign In
              </Button>
            )}

            <Button
              onClick={() => setIsCalendarOpen(true)}
              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-poppins px-4 sm:px-6 py-2 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/25 text-sm sm:text-base backdrop-blur-md border border-white/20"
            >
              Book a Call
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden text-gray-900 dark:text-white p-2"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 bg-white/95 dark:bg-black/95 backdrop-blur-xl border-b border-white/30 dark:border-white/20 shadow-lg">
            <div className="flex flex-col space-y-4 px-4 py-6">
              <button
                onClick={() => scrollToSection('home')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Home
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                About
              </button>
              <button
                onClick={() => scrollToSection('services')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Services
              </button>
              <button
                onClick={() => scrollToSection('portfolio')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Portfolio
              </button>
              <button
                onClick={() => scrollToSection('pricing')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Pricing
              </button>
              <Link
                to="/blog"
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2 block"
              >
                Blog
              </Link>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
              >
                Contact
              </button>
              <div className="flex flex-col gap-4 mt-4">
                {user ? (
                  // Authenticated user options
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={profile?.avatar_url} alt={profile?.full_name || user.email} />
                        <AvatarFallback>
                          {profile?.full_name ? profile.full_name.charAt(0).toUpperCase() : user.email?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {profile?.full_name || 'User'}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {user.email}
                        </span>
                      </div>
                    </div>
                    <Link
                      to="/admin/blog"
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="flex items-center gap-2 text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
                    >
                      <PenTool className="h-4 w-4" />
                      Blog Dashboard
                    </Link>
                    {(profile?.role === 'owner' || profile?.role === 'super_admin' || profile?.role === 'admin') && (
                      <Link
                        to="/admin/users"
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="flex items-center gap-2 text-gray-900 dark:text-white hover:text-primary transition-colors font-poppins text-left py-2"
                      >
                        <Settings className="h-4 w-4" />
                        Admin Settings
                      </Link>
                    )}
                    <button
                      onClick={() => {
                        signOut();
                        setIsMobileMenuOpen(false);
                      }}
                      className="flex items-center gap-2 text-red-600 hover:text-red-700 transition-colors font-poppins text-left py-2"
                    >
                      <LogOut className="h-4 w-4" />
                      Sign Out
                    </button>
                  </div>
                ) : (
                  // Sign in button for non-authenticated users
                  <div className="flex items-center justify-between">
                    <Button
                      onClick={() => {
                        setIsAuthModalOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      variant="outline"
                      className="border-primary text-primary hover:bg-primary/10 hover:border-primary/50 font-poppins px-4 py-2 rounded-full transition-all duration-300 flex-1 mr-3"
                    >
                      <User className="mr-2 h-4 w-4" />
                      Sign In
                    </Button>
                    <Button
                      onClick={() => {
                        setIsCalendarOpen(true);
                        setIsMobileMenuOpen(false);
                      }}
                      className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-poppins px-4 py-2 rounded-full transition-all duration-300 hover:shadow-lg hover:shadow-cyan-500/25 backdrop-blur-md border border-white/20 flex-1"
                    >
                      Book a Call
                    </Button>
                  </div>
                )}

                <div className="flex items-center justify-center mt-3">
                  <ThemeToggle />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Calendar Modal */}
      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </nav>
  );
};

export default Navigation;
