import React, { useState } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ThemeToggle } from '@/components/ThemeToggle'
import OrganizationSwitcher from '@/components/OrganizationSwitcher'
import { signOut } from '@/lib/supabase'
import {
  FileText,
  Users,
  Settings,
  Shield,
  Zap,
  Eye,
  LogOut,
  Menu,
  X,
  Plus,
  BarChart3,
  MessageSquare,
  Home,
  ChevronDown,
  User,
  Mail,
  CreditCard
} from 'lucide-react'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface AdminLayoutProps {
  children: React.ReactNode
  title?: string
  subtitle?: string
  actions?: React.ReactNode
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ 
  children, 
  title = "Dashboard",
  subtitle,
  actions 
}) => {
  const { user, profile } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
      navigate('/')
    } catch (error) {
      toast.error('Failed to sign out')
    }
  }

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/admin/blog',
      icon: Home,
      current: location.pathname === '/admin/blog'
    },
    {
      name: 'New Post',
      href: '/admin/blog/new',
      icon: Plus,
      current: location.pathname === '/admin/blog/new'
    },
    {
      name: 'Share Story',
      href: '/admin/blog/testimonial',
      icon: MessageSquare,
      current: location.pathname === '/admin/blog/testimonial'
    },
    {
      name: 'Profile',
      href: '/admin/blog/profile',
      icon: User,
      current: location.pathname === '/admin/blog/profile'
    },
    {
      name: 'Changelog',
      href: '/admin/blog/changelog',
      icon: FileText,
      current: location.pathname === '/admin/blog/changelog'
    },
    {
      name: 'Credits Usage',
      href: '/admin/credits',
      icon: CreditCard,
      current: location.pathname === '/admin/credits'
    }
  ]

  // Admin-only navigation items
  const adminNavigationItems = [
    {
      name: 'User Management',
      href: '/admin/blog/users',
      icon: Users,
      current: location.pathname === '/admin/blog/users',
      requiredRole: ['admin', 'super_admin', 'owner', 'saas_owner']
    },
    {
      name: 'Quote Requests',
      href: '/admin/quotes',
      icon: Shield,
      current: location.pathname === '/admin/quotes',
      requiredRole: ['admin', 'super_admin', 'owner', 'saas_owner']
    },
    {
      name: 'Workflows',
      href: '/admin/automations',
      icon: Zap,
      current: location.pathname === '/admin/automations',
      requiredRole: ['user', 'admin', 'super_admin', 'owner', 'saas_owner']
    },
    {
      name: 'Email Integrations',
      href: '/admin/email-integrations',
      icon: Mail,
      current: location.pathname === '/admin/email-integrations',
      requiredRole: ['user', 'admin', 'super_admin', 'owner', 'saas_owner']
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: BarChart3,
      current: location.pathname === '/admin/analytics',
      requiredRole: ['user', 'admin', 'super_admin', 'owner', 'saas_owner']
    }
  ]

  // SaaS Owner exclusive navigation items
  const saasOwnerNavigationItems = [
    {
      name: 'Billing Management',
      href: '/admin/billing',
      icon: CreditCard,
      current: location.pathname === '/admin/billing',
      requiredRole: ['saas_owner']
    }
  ]

  const hasPermission = (requiredRoles: string[]) => {
    return profile?.role && requiredRoles.includes(profile.role)
  }

  const SidebarContent = () => (
    <div className="flex flex-col h-full">
      {/* Logo/Brand */}
      <div className="flex items-center gap-3 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-lg flex items-center justify-center">
          <span className="text-white font-bold text-sm">M</span>
        </div>
        <div>
          <h1 className="font-montserrat font-bold text-lg text-gray-900 dark:text-white">
            MBI Admin
          </h1>
        </div>
      </div>

      {/* User Profile Section */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={profile?.avatar_url} alt={profile?.full_name || user?.email} />
            <AvatarFallback>
              {profile?.full_name ? profile.full_name.charAt(0).toUpperCase() : user?.email?.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {profile?.full_name || 'User'}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
              {user?.email}
            </p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {navigationItems.map((item) => (
          <Link
            key={item.name}
            to={item.href}
            onClick={() => setSidebarOpen(false)}
            className={cn(
              'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
              item.current
                ? 'bg-primary text-white'
                : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
            )}
          >
            <item.icon
              className={cn(
                'mr-3 h-5 w-5 flex-shrink-0',
                item.current
                  ? 'text-white'
                  : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
              )}
            />
            {item.name}
          </Link>
        ))}

        {/* Admin Section */}
        {adminNavigationItems.some(item => hasPermission(item.requiredRole)) && (
          <>
            <div className="pt-4 pb-2">
              <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Administration
              </h3>
            </div>
            {adminNavigationItems.map((item) =>
              hasPermission(item.requiredRole) && (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    item.current
                      ? 'bg-primary text-white'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  )}
                >
                  <item.icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      item.current
                        ? 'text-white'
                        : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                    )}
                  />
                  {item.name}
                </Link>
              )
            )}
          </>
        )}

        {/* SaaS Owner Section */}
        {saasOwnerNavigationItems.some(item => hasPermission(item.requiredRole)) && (
          <>
            <div className="pt-4 pb-2">
              <h3 className="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                SaaS Management
              </h3>
            </div>
            {saasOwnerNavigationItems.map((item) =>
              hasPermission(item.requiredRole) && (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setSidebarOpen(false)}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                    item.current
                      ? 'bg-primary text-white'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  )}
                >
                  <item.icon
                    className={cn(
                      'mr-3 h-5 w-5 flex-shrink-0',
                      item.current
                        ? 'text-white'
                        : 'text-gray-400 group-hover:text-gray-500 dark:group-hover:text-gray-300'
                    )}
                  />
                  {item.name}
                </Link>
              )
            )}
          </>
        )}
      </nav>

      {/* Bottom Actions */}
      <div className="px-4 py-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
        <Link to="/blog">
          <Button variant="outline" size="sm" className="w-full justify-start">
            <Eye className="h-4 w-4 mr-2" />
            View Blog
          </Button>
        </Link>
        <Button
          onClick={handleSignOut}
          variant="ghost"
          size="sm"
          className="w-full justify-start text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  )

  const SidebarContentMobile = () => (
    <div className="flex flex-col h-full">
      <SidebarContent />
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 transform transition-transform duration-300 ease-in-out lg:hidden",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="absolute top-4 right-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        <SidebarContentMobile />
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <SidebarContent />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Top header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="lg:hidden"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
                <div>
                  <h1 className="text-xl sm:text-2xl font-montserrat font-bold text-gray-900 dark:text-white">
                    {title}
                  </h1>
                  {subtitle && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 font-poppins">
                      {subtitle}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-4">
                <OrganizationSwitcher />
                <ThemeToggle />
                {actions}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="px-4 sm:px-6 lg:px-8 py-8">
          {children}
        </main>
      </div>
    </div>
  )
}

export default AdminLayout
