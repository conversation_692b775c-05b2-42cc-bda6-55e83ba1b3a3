import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { createEmailIntegration, updateEmailIntegration, type EmailIntegration } from '@/lib/supabase'
import { toast } from 'sonner'
import { Loader2, Eye, EyeOff, Info } from 'lucide-react'

interface EmailIntegrationFormProps {
  integration?: EmailIntegration | null
  onSuccess: () => void
  onCancel: () => void
}

const EmailIntegrationForm: React.FC<EmailIntegrationFormProps> = ({
  integration,
  onSuccess,
  onCancel
}) => {
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    provider: 'gmail' as EmailIntegration['provider'],
    is_active: true,
    is_default: false,
    config: {}
  })

  useEffect(() => {
    if (integration) {
      setFormData({
        name: integration.name,
        provider: integration.provider,
        is_active: integration.is_active,
        is_default: integration.is_default,
        config: integration.config
      })
    }
  }, [integration])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      if (integration) {
        const { error } = await updateEmailIntegration(integration.id, formData)
        if (error) {
          toast.error('Failed to update integration')
        } else {
          toast.success('Integration updated successfully')
          onSuccess()
        }
      } else {
        const { error } = await createEmailIntegration(formData)
        if (error) {
          toast.error('Failed to create integration')
        } else {
          toast.success('Integration created successfully')
          onSuccess()
        }
      }
    } catch (error) {
      toast.error('An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const updateConfig = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [key]: value
      }
    }))
  }

  const renderProviderConfig = () => {
    switch (formData.provider) {
      case 'gmail':
        return (
          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">✅ Gmail Setup (Server-Side SMTP)</h4>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Gmail now works with real email sending! Enable 2-factor authentication and create an App Password.
                  </p>
                  <div className="mt-2 space-y-1">
                    <a
                      href="https://myaccount.google.com/apppasswords"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-1 text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 underline"
                    >
                      Create App Password →
                    </a>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      💡 Uses secure server-side SMTP - no fees required!
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="gmail-email">Gmail Address</Label>
              <Input
                id="gmail-email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.email || ''}
                onChange={(e) => updateConfig('email', e.target.value)}
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Use your actual Gmail address (the one you created the app password for)
              </p>
            </div>

            <div>
              <Label htmlFor="gmail-from-name">Sender Name</Label>
              <Input
                id="gmail-from-name"
                type="text"
                placeholder="Your Name or Company Name"
                value={formData.config.fromName || ''}
                onChange={(e) => updateConfig('fromName', e.target.value)}
              />
              <p className="text-xs text-gray-500 mt-1">
                The name that will appear as the sender (e.g., "MBI Team", "John Smith")
              </p>
            </div>
            
            <div>
              <Label htmlFor="gmail-password">App Password</Label>
              <div className="relative">
                <Input
                  id="gmail-password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="16-character app password (xxxx xxxx xxxx xxxx)"
                  value={formData.config.appPassword || ''}
                  onChange={(e) => updateConfig('appPassword', e.target.value.replace(/\s/g, ''))}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Spaces are automatically removed. Paste your 16-character app password with or without spaces.
              </p>
            </div>
          </div>
        )

      case 'outlook':
        return (
          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">✅ Outlook Setup (Server-Side SMTP)</h4>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Outlook now works with real email sending! Use your regular password or app password if 2FA is enabled.
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                    💡 Uses secure server-side SMTP - no fees required!
                  </p>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="outlook-email">Outlook Email</Label>
              <Input
                id="outlook-email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.email || ''}
                onChange={(e) => updateConfig('email', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="outlook-password">Password</Label>
              <div className="relative">
                <Input
                  id="outlook-password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Your password or app password"
                  value={formData.config.password || ''}
                  onChange={(e) => updateConfig('password', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          </div>
        )

      case 'resend':
        return (
          <div className="space-y-4">
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-purple-600 dark:text-purple-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-purple-900 dark:text-purple-100">Resend Setup</h4>
                  <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                    Get your API key from your Resend dashboard
                  </p>
                  <a
                    href="https://resend.com/api-keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 mt-2 underline"
                  >
                    Get API Key →
                  </a>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="resend-api-key">API Key</Label>
              <div className="relative">
                <Input
                  id="resend-api-key"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="re_..."
                  value={formData.config.apiKey || ''}
                  onChange={(e) => updateConfig('apiKey', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div>
              <Label htmlFor="resend-from">From Email</Label>
              <Input
                id="resend-from"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.fromEmail || ''}
                onChange={(e) => updateConfig('fromEmail', e.target.value)}
                required
              />
            </div>
          </div>
        )

      case 'postmark':
        return (
          <div className="space-y-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-900 dark:text-yellow-100">Postmark Setup</h4>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    Get your Server API Token from your Postmark server settings
                  </p>
                  <a
                    href="https://account.postmarkapp.com/servers"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-300 mt-2 underline"
                  >
                    Postmark Servers →
                  </a>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="postmark-token">Server API Token</Label>
              <div className="relative">
                <Input
                  id="postmark-token"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Your Postmark server token"
                  value={formData.config.serverToken || ''}
                  onChange={(e) => updateConfig('serverToken', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div>
              <Label htmlFor="postmark-from">From Email</Label>
              <Input
                id="postmark-from"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.fromEmail || ''}
                onChange={(e) => updateConfig('fromEmail', e.target.value)}
                required
              />
            </div>
          </div>
        )

      case 'sendgrid':
        return (
          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">SendGrid Setup</h4>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Create an API key in your SendGrid dashboard with Mail Send permissions
                  </p>
                  <a
                    href="https://app.sendgrid.com/settings/api_keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 mt-2 underline"
                  >
                    Create API Key →
                  </a>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="sendgrid-api-key">API Key</Label>
              <div className="relative">
                <Input
                  id="sendgrid-api-key"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="SG..."
                  value={formData.config.apiKey || ''}
                  onChange={(e) => updateConfig('apiKey', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div>
              <Label htmlFor="sendgrid-from">From Email</Label>
              <Input
                id="sendgrid-from"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.fromEmail || ''}
                onChange={(e) => updateConfig('fromEmail', e.target.value)}
                required
              />
            </div>
          </div>
        )

      case 'mailgun':
        return (
          <div className="space-y-4">
            <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-orange-900 dark:text-orange-100">Mailgun Setup</h4>
                  <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                    Get your API key and domain from your Mailgun dashboard
                  </p>
                </div>
              </div>
            </div>
            
            <div>
              <Label htmlFor="mailgun-api-key">API Key</Label>
              <div className="relative">
                <Input
                  id="mailgun-api-key"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="key-..."
                  value={formData.config.apiKey || ''}
                  onChange={(e) => updateConfig('apiKey', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div>
              <Label htmlFor="mailgun-domain">Domain</Label>
              <Input
                id="mailgun-domain"
                placeholder="mg.yourdomain.com"
                value={formData.config.domain || ''}
                onChange={(e) => updateConfig('domain', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="mailgun-from">From Email</Label>
              <Input
                id="mailgun-from"
                type="email"
                placeholder="<EMAIL>"
                value={formData.config.fromEmail || ''}
                onChange={(e) => updateConfig('fromEmail', e.target.value)}
                required
              />
            </div>
          </div>
        )

      case 'smtp':
        return (
          <div className="space-y-4">
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                <div>
                  <h4 className="font-medium text-green-900 dark:text-green-100">✅ Custom SMTP Setup (Server-Side)</h4>
                  <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                    Configure any SMTP-compatible email provider with real email sending capability.
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                    💡 Uses secure server-side SMTP - works with any provider!
                  </p>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="smtp-host">SMTP Host</Label>
                <Input
                  id="smtp-host"
                  placeholder="smtp.example.com"
                  value={formData.config.host || ''}
                  onChange={(e) => updateConfig('host', e.target.value)}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="smtp-port">Port</Label>
                <Input
                  id="smtp-port"
                  type="number"
                  placeholder="587"
                  value={formData.config.port || ''}
                  onChange={(e) => updateConfig('port', parseInt(e.target.value))}
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="smtp-username">Username</Label>
              <Input
                id="smtp-username"
                placeholder="<EMAIL>"
                value={formData.config.username || ''}
                onChange={(e) => updateConfig('username', e.target.value)}
                required
              />
            </div>
            
            <div>
              <Label htmlFor="smtp-password">Password</Label>
              <div className="relative">
                <Input
                  id="smtp-password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Your password"
                  value={formData.config.password || ''}
                  onChange={(e) => updateConfig('password', e.target.value)}
                  required
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="smtp-secure"
                checked={formData.config.secure || false}
                onCheckedChange={(checked) => updateConfig('secure', checked)}
              />
              <Label htmlFor="smtp-secure">Use TLS/SSL</Label>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name">Integration Name</Label>
          <Input
            id="name"
            placeholder="My Email Service"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            required
          />
        </div>
        
        <div>
          <Label htmlFor="provider">Email Provider</Label>
          <Select
            value={formData.provider}
            onValueChange={(value) => setFormData(prev => ({ 
              ...prev, 
              provider: value as EmailIntegration['provider'],
              config: {} // Reset config when provider changes
            }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gmail">Gmail</SelectItem>
              <SelectItem value="outlook">Outlook</SelectItem>
              <SelectItem value="resend">Resend</SelectItem>
              <SelectItem value="postmark">Postmark</SelectItem>
              <SelectItem value="sendgrid">SendGrid</SelectItem>
              <SelectItem value="mailgun">Mailgun</SelectItem>
              <SelectItem value="smtp">Custom SMTP</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Provider Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          {renderProviderConfig()}
        </CardContent>
      </Card>

      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="is-active"
            checked={formData.is_active}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: !!checked }))}
          />
          <Label htmlFor="is-active">Active</Label>
        </div>
        
        <div className="flex items-center space-x-2">
          <Checkbox
            id="is-default"
            checked={formData.is_default}
            onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_default: !!checked }))}
          />
          <Label htmlFor="is-default">Set as default</Label>
        </div>
      </div>

      <div className="flex gap-2 justify-end">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : null}
          {integration ? 'Update' : 'Create'} Integration
        </Button>
      </div>
    </form>
  )
}

export default EmailIntegrationForm
