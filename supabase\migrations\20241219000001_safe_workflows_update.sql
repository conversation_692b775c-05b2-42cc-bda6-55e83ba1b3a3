-- Safe migration to add templates and email features
-- This handles existing tables and policies gracefully

-- Add is_template column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workflows' AND column_name = 'is_template'
    ) THEN
        ALTER TABLE workflows ADD COLUMN is_template BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Update existing workflows to not be templates
UPDATE workflows SET is_template = false WHERE is_template IS NULL;

-- Create email integrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  provider TEXT NOT NULL CHECK (provider IN ('gmail', 'outlook', 'resend', 'postmark', 'sendgrid', 'mailgun', 'smtp')),
  config <PERSON>SO<PERSON>B NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create email templates table if it doesn't exist
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  integration_id UUID REFERENCES email_integrations(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  body TEXT NOT NULL,
  variables JSONB DEFAULT '[]',
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_workflows_is_template ON workflows(is_template);
CREATE INDEX IF NOT EXISTS idx_email_integrations_user_id ON email_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_email_integrations_active ON email_integrations(is_active);
CREATE INDEX IF NOT EXISTS idx_email_templates_user_id ON email_templates(user_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_integration_id ON email_templates(integration_id);

-- Enable RLS for new tables
ALTER TABLE email_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DO $$ 
BEGIN
    -- Email integrations policies
    DROP POLICY IF EXISTS "Users can view their own email integrations" ON email_integrations;
    DROP POLICY IF EXISTS "Users can create email integrations" ON email_integrations;
    DROP POLICY IF EXISTS "Users can update their own email integrations" ON email_integrations;
    DROP POLICY IF EXISTS "Users can delete their own email integrations" ON email_integrations;
    DROP POLICY IF EXISTS "Admins can manage all email integrations" ON email_integrations;
    
    -- Email templates policies
    DROP POLICY IF EXISTS "Users can view their own email templates" ON email_templates;
    DROP POLICY IF EXISTS "Users can create email templates" ON email_templates;
    DROP POLICY IF EXISTS "Users can update their own email templates" ON email_templates;
    DROP POLICY IF EXISTS "Users can delete their own email templates" ON email_templates;
    DROP POLICY IF EXISTS "Admins can manage all email templates" ON email_templates;
END $$;

-- Create RLS policies for email integrations
CREATE POLICY "Users can view their own email integrations" ON email_integrations
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create email integrations" ON email_integrations
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own email integrations" ON email_integrations
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own email integrations" ON email_integrations
  FOR DELETE USING (user_id = auth.uid());

-- Admin users can manage all email integrations
CREATE POLICY "Admins can manage all email integrations" ON email_integrations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create RLS policies for email templates
CREATE POLICY "Users can view their own email templates" ON email_templates
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create email templates" ON email_templates
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own email templates" ON email_templates
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own email templates" ON email_templates
  FOR DELETE USING (user_id = auth.uid());

-- Admin users can manage all email templates
CREATE POLICY "Admins can manage all email templates" ON email_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create triggers for updated_at if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_email_integrations_updated_at'
    ) THEN
        CREATE TRIGGER update_email_integrations_updated_at
          BEFORE UPDATE ON email_integrations
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_email_templates_updated_at'
    ) THEN
        CREATE TRIGGER update_email_templates_updated_at
          BEFORE UPDATE ON email_templates
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON email_integrations TO authenticated;
GRANT ALL ON email_templates TO authenticated;
