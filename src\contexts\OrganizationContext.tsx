import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { supabase } from '@/lib/supabase'

interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  avatar_url?: string
  subscription_plan: 'free' | 'basic' | 'pro' | 'enterprise'
  subscription_status: 'active' | 'cancelled' | 'past_due' | 'trialing'
  workflow_credits_used: number
  workflow_credits_limit: number
  ai_credits_used: number
  ai_credits_limit: number
  credits_reset_date: string
  stripe_customer_id?: string
  stripe_subscription_id?: string
  created_at: string
  updated_at: string
}

interface OrganizationMember {
  id: string
  organization_id: string
  user_id: string
  role: 'owner' | 'admin' | 'editor' | 'viewer'
  invited_by?: string
  invited_at: string
  joined_at?: string
  is_active: boolean
}

interface OrganizationContextType {
  currentOrganization: Organization | null
  organizations: Organization[]
  members: OrganizationMember[]
  loading: boolean
  error: string | null
  
  // Actions
  switchOrganization: (orgId: string) => Promise<void>
  refreshOrganization: () => Promise<void>
  checkCredits: (type: 'workflow' | 'ai', amount?: number) => boolean
  consumeCredits: (type: 'workflow' | 'ai', amount: number, metadata?: any) => Promise<boolean>
  
  // Computed values
  workflowCreditsRemaining: number
  aiCreditsRemaining: number
  isNearWorkflowLimit: boolean
  isNearAiLimit: boolean
  canUseWorkflows: boolean
  canUseAI: boolean
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined)

export const useOrganization = () => {
  const context = useContext(OrganizationContext)
  if (context === undefined) {
    throw new Error('useOrganization must be used within an OrganizationProvider')
  }
  return context
}

export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, profile } = useAuth()
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null)
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [members, setMembers] = useState<OrganizationMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load user's organizations
  const loadOrganizations = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      let orgs: Organization[] = []

      // SaaS Owner can see all organizations
      if (profile?.role === 'saas_owner') {
        const { data: allOrgs, error: allOrgsError } = await supabase
          .from('organizations')
          .select('*')
          .order('created_at', { ascending: false })

        if (allOrgsError) throw allOrgsError
        orgs = allOrgs || []
      } else {
        // Regular users: Get organizations user is a member of
        const { data: memberData, error: memberError } = await supabase
          .from('organization_members')
          .select(`
            *,
            organization:organizations(*)
          `)
          .eq('user_id', user.id)
          .eq('is_active', true)

        if (memberError) throw memberError
        orgs = memberData?.map(m => m.organization).filter(Boolean) || []
      }

      setOrganizations(orgs)

      // Set current organization (first one or user's personal org)
      if (orgs.length > 0 && !currentOrganization) {
        const personalOrg = orgs.find(org => org.slug === `user-${user.id}`)
        setCurrentOrganization(personalOrg || orgs[0])
      } else if (orgs.length === 0) {
        // User has no organizations - this is expected for new users
        console.log('User has no organizations yet - this is normal for new users')
        setCurrentOrganization(null)
      }

    } catch (err) {
      console.error('Error loading organizations:', err)
      setError(err instanceof Error ? err.message : 'Failed to load organizations')
    } finally {
      setLoading(false)
    }
  }

  // Load organization members
  const loadMembers = async (orgId: string) => {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .select('*')
        .eq('organization_id', orgId)
        .eq('is_active', true)

      if (error) throw error
      setMembers(data || [])
    } catch (err) {
      console.error('Error loading members:', err)
    }
  }

  // Switch to different organization
  const switchOrganization = async (orgId: string) => {
    const org = organizations.find(o => o.id === orgId)
    if (org) {
      setCurrentOrganization(org)
      await loadMembers(orgId)
      
      // Store preference
      localStorage.setItem('currentOrganizationId', orgId)
    }
  }

  // Refresh current organization data
  const refreshOrganization = async () => {
    if (!currentOrganization) return

    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', currentOrganization.id)
        .single()

      if (error) throw error
      if (data) {
        setCurrentOrganization(data)
        // Update in organizations list too
        setOrganizations(prev => 
          prev.map(org => org.id === data.id ? data : org)
        )
      }
    } catch (err) {
      console.error('Error refreshing organization:', err)
    }
  }

  // Check if organization has enough credits
  const checkCredits = (type: 'workflow' | 'ai', amount: number = 1): boolean => {
    if (!currentOrganization) return false

    const used = type === 'workflow' 
      ? currentOrganization.workflow_credits_used 
      : currentOrganization.ai_credits_used
    
    const limit = type === 'workflow' 
      ? currentOrganization.workflow_credits_limit 
      : currentOrganization.ai_credits_limit

    // -1 means unlimited
    if (limit === -1) return true
    
    return (used + amount) <= limit
  }

  // Consume credits
  const consumeCredits = async (
    type: 'workflow' | 'ai',
    amount: number,
    metadata?: any
  ): Promise<boolean> => {
    if (!currentOrganization || !checkCredits(type, amount)) {
      return false
    }

    try {
      // Call the appropriate database function to consume credits
      const functionName = type === 'workflow' ? 'consume_workflow_credits' : 'consume_ai_credits'
      const { data, error } = await supabase.rpc(functionName, {
        org_id: currentOrganization.id,
        credits_to_consume: amount,
        action_type: type === 'workflow' ? 'workflow_execution' : 'ai_request',
        metadata: metadata
      })

      if (error) throw error

      if (data) {
        // Refresh organization data
        await refreshOrganization()
        return true
      }

      return false
    } catch (err) {
      console.error('Error consuming credits:', err)
      return false
    }
  }

  // Computed values
  const workflowCreditsRemaining = currentOrganization 
    ? (currentOrganization.workflow_credits_limit === -1 
        ? Infinity 
        : Math.max(0, currentOrganization.workflow_credits_limit - currentOrganization.workflow_credits_used))
    : 0

  const aiCreditsRemaining = currentOrganization 
    ? (currentOrganization.ai_credits_limit === -1 
        ? Infinity 
        : Math.max(0, currentOrganization.ai_credits_limit - currentOrganization.ai_credits_used))
    : 0

  const isNearWorkflowLimit = currentOrganization 
    ? (currentOrganization.workflow_credits_limit > 0 && 
       (currentOrganization.workflow_credits_used / currentOrganization.workflow_credits_limit) >= 0.8)
    : false

  const isNearAiLimit = currentOrganization 
    ? (currentOrganization.ai_credits_limit > 0 && 
       (currentOrganization.ai_credits_used / currentOrganization.ai_credits_limit) >= 0.8)
    : false

  const canUseWorkflows = checkCredits('workflow', 1)
  const canUseAI = checkCredits('ai', 1)

  // Load data when user or profile changes
  useEffect(() => {
    if (user && profile) {
      loadOrganizations()
    } else {
      setCurrentOrganization(null)
      setOrganizations([])
      setMembers([])
    }
  }, [user, profile])

  // Load members when current organization changes
  useEffect(() => {
    if (currentOrganization) {
      loadMembers(currentOrganization.id)
    }
  }, [currentOrganization])

  // Restore saved organization preference
  useEffect(() => {
    const savedOrgId = localStorage.getItem('currentOrganizationId')
    if (savedOrgId && organizations.length > 0) {
      const savedOrg = organizations.find(org => org.id === savedOrgId)
      if (savedOrg) {
        setCurrentOrganization(savedOrg)
      }
    }
  }, [organizations])

  const value: OrganizationContextType = {
    currentOrganization,
    organizations,
    members,
    loading,
    error,
    
    switchOrganization,
    refreshOrganization,
    checkCredits,
    consumeCredits,
    
    workflowCreditsRemaining,
    aiCreditsRemaining,
    isNearWorkflowLimit,
    isNearAiLimit,
    canUseWorkflows,
    canUseAI
  }

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  )
}
