import { supabase } from '@/lib/supabase'

export interface WorkflowNode {
  id: string
  type: 'trigger' | 'condition' | 'action'
  position: { x: number; y: number }
  data: any
  connections: string[]
}

export interface Workflow {
  id: string
  name: string
  description?: string
  nodes: WorkflowNode[]
  active: boolean
  is_template?: boolean
  created_by: string
  created_at: string
  updated_at: string
}

export interface WorkflowExecution {
  id: string
  workflow_id: string
  workflow_name: string
  trigger_data?: any
  status: 'success' | 'failed' | 'pending'
  error_message?: string
  execution_time_ms?: number
  executed_at: string
}

export class WorkflowService {
  // Get all workflows for the current organization (excluding templates)
  static async getWorkflows(organizationId?: string): Promise<Workflow[]> {
    let query = supabase
      .from('workflows')
      .select('*')
      .eq('is_template', false)
      .order('created_at', { ascending: false })

    // Filter by organization if provided
    if (organizationId) {
      query = query.eq('organization_id', organizationId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching workflows:', error)
      throw error
    }

    return data || []
  }

  // Get a specific workflow by ID
  static async getWorkflow(id: string): Promise<Workflow | null> {
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // Workflow not found
      }
      console.error('Error fetching workflow:', error)
      throw error
    }

    return data
  }

  // Create a new workflow
  static async createWorkflow(
    workflow: Omit<Workflow, 'id' | 'created_by' | 'created_at' | 'updated_at'>,
    organizationId?: string
  ): Promise<Workflow> {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      throw new Error('User not authenticated')
    }

    const { data, error } = await supabase
      .from('workflows')
      .insert({
        ...workflow,
        created_by: user.id,
        organization_id: organizationId
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating workflow:', error)
      throw error
    }

    return data
  }

  // Update an existing workflow
  static async updateWorkflow(id: string, updates: Partial<Omit<Workflow, 'id' | 'created_by' | 'created_at' | 'updated_at'>>): Promise<Workflow> {
    const { data, error } = await supabase
      .from('workflows')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating workflow:', error)
      throw error
    }

    return data
  }

  // Delete a workflow
  static async deleteWorkflow(id: string): Promise<void> {
    const { error } = await supabase
      .from('workflows')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting workflow:', error)
      throw error
    }
  }

  // Toggle workflow active status
  static async toggleWorkflowStatus(id: string, active: boolean): Promise<Workflow> {
    return this.updateWorkflow(id, { active })
  }

  // Get all workflow templates
  static async getWorkflowTemplates(): Promise<Workflow[]> {
    const { data, error } = await supabase
      .from('workflows')
      .select('*')
      .eq('is_template', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching workflow templates:', error)
      throw error
    }

    return data || []
  }

  // Create a template from an existing workflow
  static async createTemplate(workflowId: string, templateName?: string): Promise<Workflow> {
    // Get the original workflow
    const originalWorkflow = await this.getWorkflow(workflowId)
    if (!originalWorkflow) {
      throw new Error('Workflow not found')
    }

    // Create template
    const templateData = {
      name: templateName || `${originalWorkflow.name} Template`,
      description: `Template based on: ${originalWorkflow.description || originalWorkflow.name}`,
      nodes: originalWorkflow.nodes,
      active: false,
      is_template: true
    }

    return this.createWorkflow(templateData)
  }

  // Delete a template
  static async deleteTemplate(templateId: string): Promise<void> {
    const { error } = await supabase
      .from('workflows')
      .delete()
      .eq('id', templateId)
      .eq('is_template', true)

    if (error) {
      console.error('Error deleting template:', error)
      throw error
    }
  }

  // Get workflow execution logs for the current organization
  static async getWorkflowExecutions(workflowId?: string, organizationId?: string, limit: number = 50): Promise<WorkflowExecution[]> {
    let query = supabase
      .from('workflow_executions')
      .select(`
        *,
        workflow:workflows(organization_id)
      `)
      .order('executed_at', { ascending: false })
      .limit(limit)

    if (workflowId) {
      query = query.eq('workflow_id', workflowId)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching workflow executions:', error)
      throw error
    }

    // Filter by organization if provided
    let executions = data || []
    if (organizationId) {
      executions = executions.filter(execution =>
        execution.workflow?.organization_id === organizationId
      )
    }

    return executions
  }

  // Log a workflow execution
  static async logExecution(
    workflowId: string,
    workflowName: string,
    triggerData?: any,
    status: 'success' | 'failed' | 'pending' = 'pending',
    errorMessage?: string,
    executionTimeMs?: number
  ): Promise<string> {
    const { data, error } = await supabase.rpc('log_workflow_execution', {
      p_workflow_id: workflowId,
      p_workflow_name: workflowName,
      p_trigger_data: triggerData,
      p_status: status,
      p_error_message: errorMessage,
      p_execution_time_ms: executionTimeMs
    })

    if (error) {
      console.error('Error logging workflow execution:', error)
      throw error
    }

    return data
  }

  // Execute a workflow with actual logic
  static async executeWorkflow(workflow: Workflow, triggerData?: any): Promise<void> {
    const startTime = Date.now()
    const executionId = await this.logExecution(workflow.id, workflow.name, triggerData, 'pending')

    try {
      // Process workflow nodes in order
      await this.processWorkflowNodes(workflow, triggerData)

      const executionTime = Date.now() - startTime

      // Update execution log with success
      await supabase
        .from('workflow_executions')
        .update({
          status: 'success',
          execution_time_ms: executionTime
        })
        .eq('id', executionId)

    } catch (error) {
      const executionTime = Date.now() - startTime

      // Update execution log with failure
      await supabase
        .from('workflow_executions')
        .update({
          status: 'failed',
          error_message: error instanceof Error ? error.message : 'Unknown error',
          execution_time_ms: executionTime
        })
        .eq('id', executionId)

      throw error
    }
  }

  // Process workflow nodes based on connections
  private static async processWorkflowNodes(workflow: Workflow, triggerData?: any): Promise<void> {
    // Find trigger nodes (starting points)
    const triggerNodes = workflow.nodes.filter(node => node.type === 'trigger')

    if (triggerNodes.length === 0) {
      throw new Error('No trigger nodes found in workflow')
    }

    // Process each trigger node's flow
    for (const triggerNode of triggerNodes) {
      await this.processNodeFlow(triggerNode, workflow.nodes, triggerData)
    }
  }

  // Process a node and its connected nodes
  private static async processNodeFlow(
    currentNode: WorkflowNode,
    allNodes: WorkflowNode[],
    triggerData?: any
  ): Promise<void> {
    console.log(`Processing node: ${currentNode.id} (${currentNode.type})`)

    // Execute current node
    await this.executeNode(currentNode, triggerData)

    // Process connected nodes
    for (const connectionId of currentNode.connections) {
      const connectedNode = allNodes.find(node => node.id === connectionId)
      if (connectedNode) {
        await this.processNodeFlow(connectedNode, allNodes, triggerData)
      }
    }
  }

  // Execute a single node
  private static async executeNode(node: WorkflowNode, triggerData?: any): Promise<void> {
    switch (node.type) {
      case 'trigger':
        // Triggers don't need execution, they just start the flow
        console.log(`Trigger executed: ${node.data.triggerType}`)
        break

      case 'condition':
        // TODO: Implement condition logic
        console.log(`Condition executed: ${node.data.conditionType}`)
        break

      case 'action':
        await this.executeAction(node, triggerData)
        break

      default:
        console.warn(`Unknown node type: ${node.type}`)
    }
  }

  // Execute an action node
  private static async executeAction(node: WorkflowNode, triggerData?: any): Promise<void> {
    const { actionType, config } = node.data

    switch (actionType) {
      case 'send_email':
      case 'email':
        await this.executeEmailAction(config, triggerData)
        break

      case 'webhook':
        await this.executeWebhookAction(config, triggerData)
        break

      case 'delay':
        await this.executeDelayAction(config, triggerData)
        break

      case 'send_notification':
        await this.executeSendNotificationAction(config, triggerData)
        break

      case 'auto_response':
        await this.executeAutoResponseAction(config, triggerData)
        break

      default:
        console.warn(`Unknown action type: ${actionType}`)
    }
  }

  // Execute email action
  private static async executeEmailAction(config: any, triggerData?: any): Promise<void> {
    if (!config.integrationId) {
      throw new Error('No email integration selected')
    }

    // Get email integration
    const { data: integration, error } = await supabase
      .from('email_integrations')
      .select('*')
      .eq('id', config.integrationId)
      .single()

    if (error || !integration) {
      throw new Error('Email integration not found')
    }

    // Process template variables
    const processedTo = this.processTemplate(config.to || '', triggerData)
    const processedSubject = this.processTemplate(config.subject || '', triggerData)
    const processedBody = this.processTemplate(config.body || '', triggerData)

    console.log('Sending email:', {
      to: processedTo,
      subject: processedSubject,
      integration: integration.name
    })

    // Send email using Supabase function
    const functionUrl = `${import.meta.env.VITE_SUPABASE_URL}/functions/v1/send-email`

    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        integrationId: config.integrationId,
        to: processedTo,
        subject: processedSubject,
        body: processedBody
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`Email sending failed: ${errorData.error || response.statusText}`)
    }

    const result = await response.json()
    console.log('Email sent successfully:', result)
  }

  // Execute webhook action
  private static async executeWebhookAction(config: any, triggerData?: any): Promise<void> {
    // TODO: Implement webhook execution
    console.log('Webhook action executed:', config)
  }

  // Execute delay action
  private static async executeDelayAction(config: any, triggerData?: any): Promise<void> {
    const delayMs = config.delayMs || 0
    console.log(`Delaying execution for ${delayMs}ms`)

    if (delayMs > 0) {
      await new Promise(resolve => setTimeout(resolve, delayMs))
    }
  }

  // Execute send notification action
  private static async executeSendNotificationAction(config: any, triggerData?: any): Promise<void> {
    console.log('Send notification action executed:', config)
    // This will be handled by the Edge Function in production
    // For frontend testing, we just log it
  }

  // Execute auto response action
  private static async executeAutoResponseAction(config: any, triggerData?: any): Promise<void> {
    console.log('Auto response action executed:', config)
    // This will be handled by the Edge Function in production
    // For frontend testing, we just log it
  }

  // Process template variables in strings
  private static processTemplate(template: string, data?: any): string {
    if (!data || !template) return template

    let processed = template

    // Replace variables like {{ contact.email }}, {{ quote.name }}, etc.
    const variableRegex = /\{\{\s*([^}]+)\s*\}\}/g

    processed = processed.replace(variableRegex, (match, variable) => {
      const trimmedVar = variable.trim()

      // Handle nested properties like contact.email
      const parts = trimmedVar.split('.')
      let value = data

      for (const part of parts) {
        if (value && typeof value === 'object' && part in value) {
          value = value[part]
        } else {
          return match // Return original if property not found
        }
      }

      return String(value || match)
    })

    // Add current date/time variables
    const now = new Date()
    processed = processed.replace(/\{\{\s*current_date\s*\}\}/g, now.toLocaleDateString())
    processed = processed.replace(/\{\{\s*current_time\s*\}\}/g, now.toLocaleTimeString())

    return processed
  }

  // Test a workflow with sample data
  static async testWorkflow(workflow: Workflow, testEmail?: string): Promise<void> {
    // Determine trigger type from workflow nodes
    const triggerNode = workflow.nodes.find(node => node.type === 'trigger')
    const triggerType = triggerNode?.data?.triggerType || 'quote_request'

    let sampleTriggerData: any = {}

    if (triggerType === 'newsletter_signup') {
      sampleTriggerData = {
        email: testEmail || '<EMAIL>',
        name: 'John Doe',
        source: 'website_test',
        subscriber_id: 'test-subscriber-123',
        subscribed_at: new Date().toISOString(),
        metadata: {
          test: true,
          referrer: 'test-workflow'
        }
      }
    } else {
      // Default to quote request data
      sampleTriggerData = {
        quote: {
          id: 'test-quote-123',
          name: 'John Doe',
          email: testEmail || '<EMAIL>',
          company: 'Test Company',
          project_type: 'Web Application',
          budget: '$50,000 - $100,000',
          industry: 'Technology',
          timeline: '3-6 months',
          key_features: ['User Authentication', 'Payment Processing', 'Admin Dashboard'],
          created_at: new Date().toISOString()
        },
        contact: {
          id: 'test-contact-123',
          name: 'John Doe',
          email: testEmail || '<EMAIL>',
          company: 'Test Company',
          phone: '+****************'
        }
      }
    }

    await this.executeWorkflow(workflow, sampleTriggerData)
  }

  // Check if workflow contains email actions
  static hasEmailActions(workflow: Workflow): boolean {
    return workflow.nodes.some(node =>
      node.type === 'action' && node.data.actionType === 'email'
    )
  }

  // Get workflow statistics for the current organization
  static async getWorkflowStats(organizationId?: string): Promise<{
    totalWorkflows: number
    activeWorkflows: number
    totalExecutions: number
    successRate: number
  }> {
    let workflowQuery = supabase.from('workflows').select('active, organization_id').eq('is_template', false)
    let executionQuery = supabase.from('workflow_executions').select(`
      status,
      workflow:workflows(organization_id)
    `)

    if (organizationId) {
      workflowQuery = workflowQuery.eq('organization_id', organizationId)
    }

    const [workflowsResult, executionsResult] = await Promise.all([
      workflowQuery,
      executionQuery
    ])

    let workflows = workflowsResult.data || []
    let executions = executionsResult.data || []

    // Filter executions by organization if provided
    if (organizationId) {
      executions = executions.filter(execution =>
        execution.workflow?.organization_id === organizationId
      )
    }

    const totalWorkflows = workflows.length
    const activeWorkflows = workflows.filter(w => w.active).length
    const totalExecutions = executions.length
    const successfulExecutions = executions.filter(e => e.status === 'success').length
    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0

    return {
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      successRate: Math.round(successRate)
    }
  }
}
