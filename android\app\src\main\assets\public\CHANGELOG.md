# Changelog

All notable changes to the Millennial Business Innovations website will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [2.0.0] - 2025-01-03

### Added
- **Progressive Web App (PWA) Implementation**
  - Service worker for offline functionality and automatic caching
  - Web app manifest for native app-like installation experience
  - Smart install prompt component with non-intrusive user experience
  - Offline support with cached static assets and app shell
  - Standalone display mode for app-like experience without browser UI
  - Custom app icons and splash screen for mobile devices
  - Theme color integration and mobile-optimized meta tags

- **Advanced Workflow Automation System**
  - Visual workflow builder with drag-and-drop node interface
  - Multiple node types: Triggers (Form Submit, Manual, Webhook), Actions (Send Email, Webhook, Delay), Conditions (future)
  - Real-time workflow execution with comprehensive logging and status tracking
  - Email integration system supporting Gmail SMTP and custom email providers
  - Liquid template language support for dynamic content in emails and webhooks
  - Workflow templates system for rapid deployment and reusability
  - Test mode for safe workflow development and debugging
  - Execution history with detailed logs and performance metrics

- **Email Integration Management**
  - Gmail SMTP integration with app password authentication
  - Custom SMTP server support for enterprise email solutions
  - Email service configuration UI with connection testing
  - Secure credential storage and validation
  - Email template system with dynamic variable support
  - Delivery tracking and error handling

### Enhanced
- **Cross-Platform Compatibility**
  - PWA enables installation on iOS, Android, Windows, macOS, and Linux
  - App-like experience across all platforms with consistent UI
  - Offline functionality ensures app works without internet connection
  - Push notification support for workflow alerts and updates

- **Automation Capabilities**
  - Complete workflow automation for lead management and customer communication
  - Integration with existing Supabase backend for seamless data flow
  - Webhook endpoints for external system integration
  - Automated email sequences with conditional logic support

### Technical Improvements
- **PWA Infrastructure**: Vite PWA plugin with Workbox service worker generation
- **Workflow Engine**: Edge function-based execution for security and performance
- **Email Services**: Server-side email processing with multiple provider support
- **Caching Strategy**: Intelligent caching for fonts, static assets, and API responses
- **Development Workflow**: Separate build commands for web, PWA, and future mobile/desktop apps

### Files Added
- `src/components/PWAInstallPrompt.tsx` - Smart PWA installation prompt
- `src/pages/admin/Automations.tsx` - Workflow management dashboard
- `src/pages/admin/WorkflowEditor.tsx` - Visual workflow builder interface
- `src/pages/admin/EmailIntegrations.tsx` - Email service configuration
- `src/components/workflow/` - Complete workflow builder component library
- `supabase/functions/execute-workflow/` - Workflow execution edge function
- `supabase/functions/send-email/` - Email sending edge function
- `scripts/generate-pwa-icons.js` - PWA icon generation utility
- `PWA_IMPLEMENTATION.md` - Complete PWA implementation documentation

### Files Modified
- `vite.config.ts` - Added VitePWA plugin configuration
- `package.json` - Added PWA dependencies and build scripts
- `index.html` - Added PWA meta tags and app manifest
- `src/main.tsx` - Added service worker registration
- `src/App.tsx` - Added PWA install prompt and workflow routes
- `src/lib/supabase.ts` - Enhanced with workflow and email integration functions

### Dependencies Added
- `vite-plugin-pwa` - PWA functionality and service worker generation
- `workbox-window` - Service worker management and updates
- `@dnd-kit/core` - Drag and drop functionality for workflow builder
- `@dnd-kit/sortable` - Sortable components for workflow nodes

## [1.9.1] - 2025-06-05

### Fixed
- **Blog Status Update Issues**
  - Fixed blog status update errors when archiving, restoring, or unpublishing posts
  - Eliminated need for page refresh after status changes with optimistic UI updates
  - Resolved function scope issues in BlogDashboard handlers
  - Added proper error handling and instant visual feedback

- **Feature Request Roadmap Enhancement**
  - Added unassigned feature requests section to roadmap for easy management
  - Fixed roadmap visibility issues for requests without status assignments
  - Enhanced drag-and-drop functionality with proper drop zones

### Added
- **Draggable Roadmap for Admins**
  - Implemented full drag-and-drop functionality using @dnd-kit libraries
  - Owner and super admin roles can now drag requests between status columns
  - Added visual indicators for drag operations with grip handles and drop zones
  - Real-time database updates when moving requests between statuses
  - Touch and mouse support for desktop and mobile devices

- **Blog Thumbnail Display**
  - Added thumbnail display support for individual blog posts
  - Enhanced blog post pages to show thumbnails when available
  - Fallback to featured images when thumbnails are not set
  - Consistent thumbnail display across blog listings and individual posts

- **Enhanced Navigation for Authenticated Users**
  - Added "Go to Dashboard" buttons on blog pages for authenticated users
  - Enhanced blog listing page with dashboard access for writers
  - Improved user experience with quick access to admin features
  - Role-based visibility for dashboard navigation elements

### Enhanced
- **State Management**
  - Implemented optimistic UI updates for all blog status operations
  - Real-time state synchronization without page refreshes
  - Improved error handling with proper rollback mechanisms
  - Enhanced user feedback with instant visual confirmations

- **Drag & Drop User Experience**
  - Added "Unassigned Requests" section for easy status assignment
  - Visual feedback with hover states and drag overlays
  - Clear instructions and badges for admin users
  - Responsive design for mobile and desktop drag operations

### Technical Improvements
- **Dependencies**: Added @dnd-kit/core, @dnd-kit/sortable, @dnd-kit/utilities
- **Performance**: Optimistic updates reduce perceived latency
- **Accessibility**: Full keyboard and screen reader support for drag operations
- **Security**: Proper role-based access controls for admin features
- **Error Handling**: Comprehensive error handling with user-friendly messages

### Files Added
- `test_feature_requests_data.sql` - Sample data for testing feature request system

### Files Modified
- `src/components/FeatureRequests.tsx` - Added complete drag-and-drop roadmap functionality
- `src/pages/admin/BlogDashboard.tsx` - Fixed status update issues and optimistic UI
- `src/pages/Blog.tsx` - Added dashboard access buttons and enhanced navigation
- `src/pages/BlogPost.tsx` - Added thumbnail display and dashboard navigation
- `src/components/RichTextEditor.tsx` - Minor improvements and optimizations
- `package.json` - Added @dnd-kit dependencies for drag-and-drop functionality

## [1.9.0] - 2025-06-05

### Added
- **Enhanced Blog Social Features**
  - Anonymous reactions: Users can like posts without signing in
  - Guest commenting system with email collection for signup invitations
  - Progressive authentication flow: Anonymous → Guest → Registered user
  - Browser fingerprinting for consistent anonymous user identification
  - Notification preferences for comment replies and promotional emails

- **Feature Request System (Canny-style)**
  - Complete feature request management with boards (Feature Requests, Integrations, Bug Reports, Improvements)
  - Roadmap view with status tracking (Planned, In Progress, Complete, On Hold, Rejected)
  - Upvoting system for both authenticated and guest users
  - Guest submission support with email collection
  - Advanced filtering by board, status, and search functionality
  - Integrated into Changelog page with tabbed interface

- **Database Enhancements**
  - Anonymous engagement tables with IP tracking and spam prevention
  - Email-based commenting system with verification tokens
  - Feature request system with boards, statuses, upvotes, and comments
  - Comprehensive RLS policies for security and privacy
  - Automated count triggers for performance optimization

### Enhanced
- **Authentication Flow**
  - Google sign-in now redirects back to the original blog post instead of dashboard
  - Progressive disclosure of guest forms for better UX
  - Smart email collection with clear value propositions
  - Gentle nudges for signup after anonymous engagement

- **Blog Engagement**
  - Visual indicators for guest vs. anonymous vs. authenticated users
  - Medium-style engagement patterns with lower friction
  - Toast notifications for signup invitations
  - Persistent anonymous state across browser sessions

- **Changelog Page**
  - Renamed to "Changelog & Feedback" with dual functionality
  - Tabbed interface combining changelog and feature requests
  - Enhanced navigation and improved user experience

### Technical Improvements
- **Security**: Comprehensive RLS policies for all new tables
- **Performance**: Automated count updates via database triggers
- **Privacy**: GDPR-compliant email collection with clear consent
- **Scalability**: Modular component architecture for feature requests
- **Analytics Ready**: IP tracking and engagement metrics collection

### Files Added
- `src/components/FeatureRequests.tsx` - Complete Canny-style feature request system
- `src/components/BlogEngagement.tsx` - Anonymous-friendly blog engagement component
- `src/components/BlogComments.tsx` - Enhanced commenting with guest support
- `src/components/BlogPromotionToast.tsx` - Signup promotion notifications
- `complete_blog_social_schema.sql` - Complete database schema for social features
- `feature_requests_schema.sql` - Feature request system database schema
- `anonymous_engagement.sql` - Anonymous user engagement enhancements

### Files Modified
- `src/lib/supabase.ts` - Added social features and feature request functions
- `src/pages/admin/Changelog.tsx` - Integrated feature request system with tabs
- `src/pages/Blog.tsx` - Added social engagement components
- `src/pages/BlogPost.tsx` - Enhanced with anonymous-friendly engagement
- `src/components/auth/AuthModal.tsx` - Improved redirect handling
- `src/components/AdminQuickAccess.tsx` - Updated for new social features

## [1.8.0] - 2025-06-04

### Added
- **Medium-Style Thumbnail Support**
  - Added `thumbnail_url` field to blog posts with database migration
  - Smart thumbnail workflow: upload images and get prompted to set as thumbnail
  - Visual thumbnail selection grid with uploaded images
  - Click-to-select interface with hover effects and visual feedback
  - Thumbnail previews in blog listings and admin dashboard
  - Manual thumbnail URL input with live preview
  - Remove thumbnail functionality with one-click deletion

- **Rich Content Embedding Capabilities**
  - **Custom HTML/JavaScript Embedding**: Full support for widgets, booking calendars, and interactive content
  - **Iframe Embedding**: YouTube, Twitter, and other iframe-based content
  - **Auto-conversion**: YouTube URLs automatically convert to embedded players
  - **TipTap Extensions**: Created custom extensions for iframe and HTML embedding
  - **JavaScript Execution**: Embedded JavaScript code executes properly in published posts

- **Enhanced Blog Editor**
  - Added embedding toolbar buttons: Monitor icon (📺) for iframes, FileCode icon (📄) for custom HTML
  - Improved rich text editor with embedding capabilities
  - Better media upload workflow with thumbnail integration
  - Enhanced visual feedback and user guidance
  - Upload progress indicators and success notifications

### Changed
- **Improved Thumbnail Workflow**
  - Enhanced UI with clear instructions and visual cues
  - Better placeholder text based on upload state
  - Improved thumbnail selection with hover effects and tooltips
  - Added emoji indicators and helpful descriptions
  - Professional thumbnail preview with remove functionality

- **Blog Post Rendering**
  - Enhanced content rendering to support embedded HTML and JavaScript
  - Improved custom HTML block processing and execution
  - Better script tag handling for dynamic content
  - Enhanced prose styling with embed support

### Technical Improvements
- **Database Schema**: Added thumbnail support with proper migrations
- **TipTap Extensions**: Custom iframe and HTML embedding extensions
- **Content Processing**: Advanced HTML processing for embedded content
- **Script Execution**: Proper JavaScript execution in blog posts
- **File Management**: Enhanced image upload tracking and management
- **UI/UX**: Comprehensive visual improvements and user feedback

### Files Added
- `add_thumbnail_support.sql` - Database migration for thumbnail support
- `src/components/tiptap-extensions/IframeExtension.ts` - Iframe embedding extension
- `src/components/tiptap-extensions/CustomHtmlExtension.ts` - Custom HTML embedding extension

### Files Modified
- `src/pages/admin/BlogEditor.tsx` - Enhanced with thumbnail selection and embedding tools
- `src/pages/BlogPost.tsx` - Improved content rendering with embed support
- `src/pages/Blog.tsx` - Added thumbnail display in blog listings
- `src/pages/admin/BlogDashboard.tsx` - Added thumbnail previews
- `src/components/RichTextEditor.tsx` - Integrated embedding extensions
- `src/lib/supabase.ts` - Updated BlogPost interface with thumbnail support

## [1.7.1] - 2025-06-04

### Fixed
- **Blog Author Display Issues**
  - Fixed author display not working when selecting different author options (Anonymous, MBI Team)
  - Resolved issue where posts still showed real name despite selecting different author display
  - Added proper database migration script for `author_display` column
  - Enhanced author display logic with proper fallback handling

- **Rich Text Formatting Issues**
  - Fixed rich text editor formatting not appearing in published blog posts
  - Enhanced prose styling with comprehensive CSS classes for all HTML elements
  - Added proper styling for headings, paragraphs, lists, links, code blocks, and blockquotes
  - Improved font consistency (Montserrat for headings, Poppins for body text)

### Added
- **Database Migration Script**
  - Created `supabase_author_display_migration.sql` for easy database setup
  - Added proper column constraints and validation for author display options
  - Included safety checks to prevent duplicate column creation

### Technical Improvements
- **Content Rendering**: Enhanced HTML content rendering with comprehensive prose classes
- **Debugging**: Added console logging for content tracking and troubleshooting
- **Database Schema**: Proper support for `author_display` field with validation
- **Error Handling**: Improved error handling for author display and content rendering

### Files Added
- `supabase_author_display_migration.sql` - Database migration script

### Files Modified
- `src/pages/admin/BlogEditor.tsx` - Fixed author_display field saving
- `src/pages/BlogPost.tsx` - Enhanced rich text styling and debugging
- `src/components/RichTextEditor.tsx` - Improved HTML output handling

## [1.7.0] - 2025-06-04

### Added
- **Author Anonymity Options**
  - Added author display preferences in blog editor: "Use Your Name", "Publish Anonymously", "Publish as MBI Team"
  - Implemented `author_display` field in BlogPost interface with three options: `real_name`, `anonymous`, `mbi_team`
  - Added `getDisplayAuthor` helper function to handle author display logic
  - Enhanced blog editor with author display selection dropdown

- **Profile Avatar Integration**
  - Added avatar display support throughout blog system
  - Profile pictures from user settings now appear on blog posts
  - Avatar fallbacks with user initials for anonymous/team posts
  - Responsive avatar sizing (small for blog list, larger for individual posts)

### Changed
- **Blog Display Enhancement**
  - Updated Blog page to show author avatars with names
  - Enhanced BlogPost page with larger author avatars
  - Updated BlogDashboard to respect author display preferences
  - Improved author information layout with better spacing

- **Date Corrections**
  - Fixed all changelog dates to reflect actual project timeline (2025)
  - Corrected version dates from December 2024 to proper June 2025 dates

### Technical Improvements
- **Database Schema**: Extended BlogPost interface with `author_display` field
- **UI Components**: Integrated Avatar component across blog pages
- **Helper Functions**: Added centralized author display logic
- **Responsive Design**: Proper avatar sizing and layout across different screen sizes

### Files Modified
- `src/lib/supabase.ts` - Added author display logic and helper function
- `src/pages/admin/BlogEditor.tsx` - Added author display selection
- `src/pages/Blog.tsx` - Added avatar support and author display
- `src/pages/BlogPost.tsx` - Enhanced with avatar and author display
- `src/pages/admin/BlogDashboard.tsx` - Updated author display logic
- `CHANGELOG.md` - Corrected all dates to proper timeline

## [1.6.0] - 2025-06-04

### Added
- **Profile Settings Page**
  - Created comprehensive profile management page at `/admin/blog/profile`
  - Added profile picture upload functionality with 5MB limit and image validation
  - Integrated Google account details by default for Google sign-ups
  - Added password reset functionality via email
  - Role badge display with color-coded hierarchy (Super Admin, Admin, Editor, Viewer)
  - Responsive design with avatar upload and form validation

- **User Management System**
  - Created admin-only user management page at `/admin/blog/users`
  - Added super admin role with elevated privileges for Stephen Jan Lovino
  - Implemented role hierarchy: super_admin > admin > editor > viewer
  - Added user statistics dashboard with role-based counts
  - Role management with proper permission controls
  - User deletion capabilities with safety restrictions
  - Password reset functionality for other users
  - Comprehensive user listing with avatars and role badges

- **Changelog Dashboard**
  - Created admin-accessible changelog page at `/admin/blog/changelog`
  - Automated parsing of CHANGELOG.md with structured display
  - Color-coded change types (Added, Changed, Fixed, Improved)
  - Version-based organization with date formatting
  - Professional UI with icons and badges for different change types

- **Enhanced Blog Dashboard**
  - Added theme toggle integration for consistent theming
  - Added navigation buttons to Profile, Users, and Changelog pages
  - Role-based visibility for admin features
  - Improved header layout with better organization

### Changed
- **Authentication System Enhancement**
  - Updated role system to include `super_admin` role
  - Modified `createOrUpdateProfile` to automatically assign super admin to Stephen Jan Lovino
  - Enhanced ProtectedRoute component to handle new role hierarchy
  - Updated role validation throughout the application

- **Database Schema Updates**
  - Extended Profile interface to support `super_admin` role
  - Added new user management functions: `updateProfile`, `getAllUsers`, `deleteUser`, `updateUserRole`
  - Enhanced profile management with avatar upload support

### Technical Improvements
- **Security Enhancements**: Implemented proper role-based access controls
- **File Management**: Added avatar upload with validation and size limits
- **Navigation**: Added new routes for profile, user management, and changelog
- **Theme Integration**: Consistent dark/light mode support across all admin pages
- **Error Handling**: Comprehensive error handling and user feedback
- **Performance**: Optimized user loading and profile management

### Files Added
- `src/pages/admin/ProfileSettings.tsx` - Complete profile management component
- `src/pages/admin/UserManagement.tsx` - Admin user management interface
- `src/pages/admin/Changelog.tsx` - Changelog display component
- `public/CHANGELOG.md` - Public changelog file for frontend access

### Files Modified
- `src/App.tsx` - Added new admin routes
- `src/lib/supabase.ts` - Enhanced with user management functions and super admin logic
- `src/components/auth/ProtectedRoute.tsx` - Updated role hierarchy
- `src/pages/admin/BlogDashboard.tsx` - Added theme toggle and navigation

## [1.5.0] - 2025-06-03

### Added
- **Blog Editor with Media Upload**
  - Created comprehensive BlogEditor component with rich form interface
  - Added auto-slug generation from title for SEO-friendly URLs
  - Implemented draft/publish workflow with proper status management
  - Added media upload functionality supporting images, videos, and GIFs (up to 10MB)
  - Integrated markdown insertion for uploaded media files
  - Added file type and size validation with user-friendly error messages
  - Created routes for `/admin/blog/new` and `/admin/blog/edit/:id`

- **Database Integration**
  - Added `getBlogPostById` function for editing existing posts by ID
  - Implemented `createOrUpdateProfile` function for user profile management
  - Added proper JSONB content format conversion for database compatibility
  - Enhanced foreign key constraint handling with automatic profile creation

- **User Experience Improvements**
  - Added loading states and error handling throughout the editor
  - Implemented proper form validation with helpful error messages
  - Added console debugging capabilities for troubleshooting
  - Created responsive editor layout with sidebar for post settings
  - Added upload progress indicators and success notifications

### Changed
- **Content Management**
  - Enhanced content field to support both string and JSON formats
  - Improved content extraction and conversion for editing existing posts
  - Added markdown support for rich text formatting
  - Updated content textarea with helpful placeholder text and tips

### Technical Improvements
- **Data Validation**: Enhanced post data structure with proper field validation
- **Error Handling**: Added comprehensive error logging and user feedback
- **File Management**: Integrated Supabase storage for media uploads
- **Route Management**: Added protected routes for blog editor access
- **Performance**: Optimized content conversion and database operations

### Files Added
- `src/pages/admin/BlogEditor.tsx` - Complete blog editor component

### Files Modified
- `src/App.tsx` - Added blog editor routes
- `src/lib/supabase.ts` - Added blog post management and profile functions

## [1.4.0] - 2025-06-02

### Added
- **Legal Pages**
  - Created comprehensive Privacy Policy page with GDPR/CCPA compliance
  - Added Terms and Conditions page with service agreements
  - Integrated legal page links in footer navigation
  - Professional legal document styling with responsive design

### Changed
- **Services Section UX Improvement**
  - Replaced multiple "Learn More" buttons with single "Book a Free Consultation" CTA
  - Streamlined user journey to reduce decision fatigue
  - Added compelling call-to-action section at bottom of services
  - Integrated calendar modal for consistent booking experience

### Fixed
- **Next.js Icon Display**
  - Fixed Next.js icon showing as white circle instead of proper logo
  - Updated to use Simple Icons CDN for reliable icon rendering
  - Improved icon fallback handling for better reliability

## [1.3.1] - 2025-06-01

### Fixed
- **Next.js Icon Display**
  - Fixed Next.js icon showing as white circle instead of proper logo
  - Updated to use latest DevIcons CDN URL for better icon rendering
  - Improved icon fallback handling for better reliability

## [1.3.0] - 2025-05-31

### Changed
- **Default Theme to Light Mode**
  - Changed default theme from "system" to "light" for consistent user experience
  - Users can still toggle between light/dark/system themes using the theme toggle
  - Provides a clean, professional first impression with light theme

### Fixed
- **Calendar Integration in Pricing Section**
  - Fixed "Schedule a Consultation" button to properly open calendar modal
  - Added consistent calendar popup behavior across all booking buttons
  - Integrated GHL calendar widget with proper state management

### Improved
- **Professional Tech Stack Icons**
  - Replaced emoji icons with high-quality SVG icons from DevIcons CDN
  - Added official brand colors for each technology (React blue, TypeScript blue, etc.)
  - Enhanced visual consistency and professional appearance
  - Implemented reliable CDN-based icon loading with fallback handling
  - Icons include: React, Next.js, TypeScript, Node.js, Python, MongoDB, PostgreSQL, AWS, Docker, Figma

### Technical Improvements
- **Icon Implementation**: Used DevIcons CDN (jsDelivr) for reliable, high-quality tech icons
- **Error Handling**: Added fallback placeholders for icons that fail to load
- **Performance**: Optimized with CDN delivery and lazy loading
- **Theme Management**: Fixed Sonner component to use custom ThemeProvider
- **Code Cleanup**: Removed react-icons dependency in favor of CDN approach

## [1.2.0] - 2025-05-30

### Fixed
- **Calendar Modal Sizing Issues**
  - Fixed calendar not being fully viewable by increasing modal size (max-w-5xl, h-90vh)
  - Removed duplicate X buttons by eliminating custom header overlay
  - Enabled scrolling for better calendar navigation on mobile devices
  - Improved modal responsiveness with w-95vw for mobile screens
  - Enhanced visual hierarchy with transparent background and shadow effects

### Added
- **Dedicated Portfolio Page**
  - Created comprehensive `/portfolio` page showcasing all 9 projects
  - Added new projects: Denti-Nexus Clinic Suite, Stephen Lovino Portfolio, MBI Corporate Site
  - Professional portfolio page with back navigation and theme toggle
  - Enhanced project cards with external link buttons and improved layouts
  - Responsive grid system optimized for all screen sizes

### Changed
- **Portfolio Section Optimization**
  - Reduced main portfolio section to 3 featured projects for better focus
  - Featured projects: Aha-Innovations Platform, RR Twins, Denti-Nexus Clinic Suite
  - "View All Projects" button now navigates to dedicated portfolio page
  - Implemented React Router navigation for seamless page transitions

### Technical Improvements
- **Routing Enhancement**: Added `/portfolio` route to App.tsx configuration
- **Component Architecture**: Created modular Portfolio page component
- **Navigation**: Professional header with back navigation on portfolio page
- **Performance**: Optimized project loading and image handling

### New Projects Showcase
- **Denti-Nexus Clinic Suite**: Healthcare platform with appointment scheduling
- **Stephen Lovino Portfolio**: Professional developer portfolio website
- **MBI Corporate Site**: Corporate website for Millennial Business Innovations
- **Enhanced Project Details**: Better descriptions and technology stack displays

## [1.1.1] - 2025-05-29

### Fixed
- **Mobile Header Overlap Issue**
  - Fixed navigation header overlapping with announcement banner on real mobile devices
  - Added proper top padding (pt-20 sm:pt-24) to hero section to account for fixed navigation
  - Enhanced navigation background with semi-transparent backdrop for better visibility
  - Improved mobile navigation spacing and backdrop blur effects

### Added
- **GHL Calendar Integration**
  - Created CalendarModal component for seamless calendar booking
  - Integrated GoHighLevel calendar widget (https://calendar.aha-innovations.com/widget/booking/muwcL091TjKbXukSZgs5)
  - Added calendar modal to both Navigation and HeroSection components
  - Professional calendar popup with proper styling and responsive design

### Changed
- **Enhanced User Experience**
  - Updated all "Book a Call" buttons to open calendar modal instead of scrolling to contact section
  - Improved navigation transparency and backdrop effects
  - Better mobile menu interaction with calendar integration
  - Calendar modal automatically loads GHL embed script when opened

### Technical Details
- **New Component**: `src/components/CalendarModal.tsx`
- **Calendar Integration**: Uses iframe embed with dynamic script loading
- **Modal System**: Built with shadcn/ui Dialog component
- **Responsive Design**: Calendar modal adapts to different screen sizes
- **Clean Script Management**: Automatic script cleanup when modal closes

## [1.1.0] - 2025-05-28

### Added
- **Complete Theme System Implementation**
  - Light/Dark/System theme toggle with persistent storage
  - Custom ThemeProvider component with React Context
  - ThemeToggle dropdown component with sun/moon icons
  - Theme toggle available in both desktop and mobile navigation
  - Automatic system theme detection and preference following

- **Comprehensive Theme-Aware Styling**
  - All sections now properly support both light and dark modes
  - Smooth transitions between theme changes
  - Proper contrast ratios for accessibility compliance
  - CSS variables integration with Tailwind's dark mode system

### Changed
- **Hero Section Content Updates**
  - Main heading: "We help founders turn ideas into seamless digital experiences" → "You dream it. We build it."
  - Subtext: "Hello, We're Millennial Business Innovations 🚀 a Full Stack Developer" → "Helping founders launch MVPs and digital products without the tech overwhelm."
  - Greeting: Added "👋 Hello, we're Millennial Business Innovations your full-stack product team." (removed em dash)
  - Applied gradient typography to "digital products" in subtext

- **Theme-Aware Component Updates**
  - **HeroSection**: Light gray gradients, adaptive text colors, theme-aware buttons
  - **AboutSection**: Light backgrounds, semi-transparent cards, adaptive text
  - **ServicesSection**: Light mode backgrounds, card styling updates
  - **PortfolioSection**: Adaptive backgrounds, border color updates
  - **PricingSection**: Light mode card styling, button color adaptations
  - **ContactSection**: Form input styling, card background updates
  - **Footer**: Light mode backgrounds, adaptive text and link colors
  - **Navigation**: Adaptive navigation bar, mobile menu theme support

- **CSS Variables Enhancement**
  - Updated light mode background from pure white to light gray (98%)
  - Enhanced dark mode with true black backgrounds
  - Improved color contrast for better readability

### Technical Details
- **Theme Storage**: Uses localStorage with key "mbi-ui-theme"
- **Default Theme**: System preference detection
- **CSS Framework**: Tailwind CSS with class-based dark mode
- **State Management**: React Context API for theme state
- **Hot Module Reload**: All changes support HMR for development

### Files Modified
- `src/components/ThemeProvider.tsx` (new)
- `src/components/ThemeToggle.tsx` (new)
- `src/App.tsx`
- `src/components/Navigation.tsx`
- `src/components/HeroSection.tsx`
- `src/components/AboutSection.tsx`
- `src/components/ServicesSection.tsx`
- `src/components/PortfolioSection.tsx`
- `src/components/PricingSection.tsx`
- `src/components/ContactSection.tsx`
- `src/components/Footer.tsx`
- `src/pages/Index.tsx`
- `src/index.css`

### Dependencies
- Existing `next-themes` package utilized for theme management
- No new dependencies added

## [1.0.0] - 2025-05-27

### Added
- Initial website launch
- Hero section with company branding
- About section with mission and vision
- Services showcase
- Portfolio gallery
- Pricing plans
- Contact form
- Footer with company information
- Responsive design for all screen sizes
- Modern UI with Tailwind CSS and shadcn/ui components

### Features
- Single-page application with smooth scrolling navigation
- Contact form with validation
- Responsive grid layouts
- Modern typography with Montserrat and Poppins fonts
- Gradient backgrounds and animations
- Mobile-first responsive design
