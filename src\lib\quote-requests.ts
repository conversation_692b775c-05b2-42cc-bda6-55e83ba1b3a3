import { supabase } from './supabase'

export interface QuoteRequest {
  id?: string
  // Project Discovery
  project_type: string
  industry: string
  target_audience: string
  
  // Requirements
  key_features: string[]
  integrations: string[]
  design_preference: string
  
  // Timeline & Budget
  timeline: string
  budget: string
  team_involvement: string
  
  // Contact
  name: string
  email: string
  company?: string
  phone?: string
  best_time_to_call?: string
  communication_preference?: string
  additional_info?: string
  
  // Metadata
  status: 'new' | 'reviewed' | 'quoted' | 'converted' | 'declined'
  created_at?: string
  updated_at?: string
  
  // Account creation
  user_id?: string
  wants_account: boolean
}

export const createQuoteRequest = async (quoteData: Omit<QuoteRequest, 'id' | 'created_at' | 'updated_at' | 'status'>) => {
  try {
    const { data, error } = await supabase
      .from('quote_requests')
      .insert({
        ...quoteData,
        status: 'new'
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating quote request:', error)
      return { data: null, error }
    }

    // Manually trigger workflows as fallback
    try {
      await triggerQuoteWorkflows(data)
    } catch (workflowError) {
      console.error('Failed to trigger workflows:', workflowError)
      // Don't fail the quote creation if workflow triggering fails
    }

    return { data, error: null }
  } catch (error) {
    console.error('Unexpected error creating quote request:', error)
    return { data: null, error }
  }
}

// Helper function to manually trigger workflows
const triggerQuoteWorkflows = async (quoteData: QuoteRequest) => {
  try {
    const triggerData = {
      quote: quoteData,
      contact: {
        id: quoteData.id,
        name: quoteData.name,
        email: quoteData.email,
        company: quoteData.company,
        phone: quoteData.phone
      },
      event: 'quote.created',
      timestamp: new Date().toISOString()
    }

    // Call the trigger-workflows function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/trigger-workflows`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        trigger_type: 'new_quote',
        trigger_data: triggerData
      })
    })

    if (!response.ok) {
      console.error('Failed to trigger workflows:', await response.text())
    } else {
      const result = await response.json()
      console.log(`Triggered ${result.triggered_count} quote workflows`)
    }
  } catch (error) {
    console.error('Error in triggerQuoteWorkflows:', error)
  }
}

export const getQuoteRequests = async () => {
  try {
    const { data, error } = await supabase
      .from('quote_requests')
      .select('*')
      .order('created_at', { ascending: false })

    return { data, error }
  } catch (error) {
    console.error('Error fetching quote requests:', error)
    return { data: null, error }
  }
}

export const updateQuoteRequestStatus = async (id: string, status: QuoteRequest['status']) => {
  try {
    const { data, error } = await supabase
      .from('quote_requests')
      .update({ status, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single()

    return { data, error }
  } catch (error) {
    console.error('Error updating quote request status:', error)
    return { data: null, error }
  }
}
