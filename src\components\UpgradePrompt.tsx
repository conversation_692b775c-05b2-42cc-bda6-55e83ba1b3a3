import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowRight, 
  Zap, 
  Sparkles, 
  Crown,
  TrendingUp,
  Users,
  Mail
} from 'lucide-react'

interface UpgradePromptProps {
  type: 'workflow' | 'ai' | 'team' | 'email'
  currentUsage: number
  limit: number
  className?: string
}

const UPGRADE_CONFIG = {
  workflow: {
    icon: Zap,
    title: "Need More Workflow Credits?",
    description: "Upgrade to get 10x more automation power",
    newLimit: "1,000 credits",
    color: "from-blue-500 to-purple-600",
    bgColor: "from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20"
  },
  ai: {
    icon: Sparkles,
    title: "Need More AI Assistance?",
    description: "Upgrade for advanced AI features",
    newLimit: "500 AI requests",
    color: "from-purple-500 to-pink-600",
    bgColor: "from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20"
  },
  team: {
    icon: Users,
    title: "Ready for Team Collaboration?",
    description: "Invite team members and work together",
    newLimit: "3 team members",
    color: "from-green-500 to-teal-600",
    bgColor: "from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20"
  },
  email: {
    icon: Mail,
    title: "Supercharge Email Automation?",
    description: "Advanced email workflows and integrations",
    newLimit: "Unlimited emails",
    color: "from-orange-500 to-red-600",
    bgColor: "from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20"
  }
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ 
  type, 
  currentUsage, 
  limit, 
  className = "" 
}) => {
  const { user, profile } = useAuth()
  const navigate = useNavigate()
  
  const config = UPGRADE_CONFIG[type]
  const IconComponent = config.icon
  
  // Don't show if user is already on paid plan
  if (!user || !profile || profile.subscription_plan !== 'free') {
    return null
  }

  // Calculate usage percentage
  const usagePercentage = limit > 0 ? (currentUsage / limit) * 100 : 0
  const isNearLimit = usagePercentage >= 80
  const isAtLimit = currentUsage >= limit

  // Only show when near or at limit
  if (!isNearLimit) {
    return null
  }

  const handleUpgrade = () => {
    navigate('/pricing')
  }

  return (
    <Card className={`bg-gradient-to-r ${config.bgColor} border-0 ${className}`}>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full bg-gradient-to-r ${config.color}`}>
              <IconComponent className="h-4 w-4 text-white" />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm">
                  {config.title}
                </h3>
                {isAtLimit && (
                  <Badge variant="destructive" className="text-xs">
                    Limit Reached
                  </Badge>
                )}
              </div>
              
              <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                {config.description}
              </p>
              
              {/* Usage Bar */}
              <div className="flex items-center gap-2 text-xs text-gray-500">
                <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <div 
                    className={`h-1.5 rounded-full transition-all duration-300 ${
                      isAtLimit 
                        ? 'bg-red-500' 
                        : isNearLimit 
                          ? 'bg-yellow-500' 
                          : 'bg-green-500'
                    }`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  />
                </div>
                <span className="whitespace-nowrap">
                  {currentUsage}/{limit === -1 ? '∞' : limit}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <div className="text-right mr-2">
              <div className="text-xs text-gray-500">Upgrade to get</div>
              <div className="text-sm font-semibold text-gray-900 dark:text-white">
                {config.newLimit}
              </div>
            </div>
            
            <Button
              onClick={handleUpgrade}
              size="sm"
              className={`bg-gradient-to-r ${config.color} hover:opacity-90 text-white`}
            >
              <Crown className="h-3 w-3 mr-1" />
              Upgrade
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        </div>

        {/* Additional benefits for at-limit state */}
        {isAtLimit && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
              <TrendingUp className="h-3 w-3" />
              <span>Join 1000+ users who upgraded for more power</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Hook for checking limits and showing prompts
export const useUpgradePrompts = () => {
  const { profile } = useAuth()

  const shouldShowUpgrade = (type: string, currentUsage: number, limit: number) => {
    if (!profile || profile.subscription_plan !== 'free') return false
    
    const usagePercentage = limit > 0 ? (currentUsage / limit) * 100 : 0
    return usagePercentage >= 80 // Show when 80% or more used
  }

  return {
    shouldShowUpgrade
  }
}

export default UpgradePrompt
