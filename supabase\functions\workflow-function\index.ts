import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  try {
    // Parse the request body
    const requestBody = await req.json()
    console.log('Received webhook:', requestBody)

    const { event, data } = requestBody

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Validate required fields
    if (!event || !data) {
      return new Response(
        JSON.stringify({ 
          error: 'Invalid payload', 
          message: 'Missing event or data fields' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Process different webhook events
    let response
    switch (event) {
      case 'quote.created':
        response = await handleNewQuote(data, supabase)
        break
      case 'quote.updated':
        response = await handleQuoteUpdate(data, supabase)
        break
      case 'quote.status_changed':
        response = await handleStatusChange(data, supabase)
        break
      default:
        return new Response(
          JSON.stringify({ 
            error: 'Unknown event type', 
            received_event: event 
          }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        )
    }

    return response

  } catch (error) {
    console.error('Webhook processing error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message,
        stack: error.stack
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function handleNewQuote(data: any, supabase: any) {
  console.log('Processing new quote:', data)

  // Log the webhook call
  try {
    await supabase.from('automation_logs').insert({
      trigger_type: 'quote.created',
      action_type: 'webhook_received',
      rendered_payload: JSON.stringify(data),
      status: 'success',
      started_at: new Date().toISOString(),
      completed_at: new Date().toISOString()
    })
  } catch (logError) {
    console.error('Failed to log webhook:', logError)
  }

  // Create GHL-friendly response
  const ghlPayload = {
    contact: {
      firstName: data.name ? data.name.split(' ')[0] : '',
      lastName: data.name ? data.name.split(' ').slice(1).join(' ') : '',
      email: data.email || '',
      phone: data.phone || '',
      companyName: data.company || ''
    },
    customFields: {
      project_type: data.project_type || '',
      industry: data.industry || '',
      budget: data.budget || '',
      timeline: data.timeline || '',
      key_features: Array.isArray(data.key_features) ? data.key_features.join(', ') : '',
      wants_blog_account: data.wants_account ? 'Yes' : 'No',
      lead_source: 'Website Quote Form',
      quote_id: data.quote_id || ''
    },
    tags: [
      'website-lead',
      data.budget ? `budget-${data.budget.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}` : 'budget-unknown',
      data.industry ? `industry-${data.industry.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}` : 'industry-unknown'
    ]
  }

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Quote webhook processed successfully',
      event: 'quote.created',
      ghl_payload: ghlPayload,
      quote_id: data.quote_id,
      processed_at: new Date().toISOString()
    }),
    {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  )
}

async function handleQuoteUpdate(data: any, supabase: any) {
  console.log('Processing quote update:', data)

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Quote update processed successfully',
      event: 'quote.updated',
      quote_id: data.quote_id,
      processed_at: new Date().toISOString()
    }),
    {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  )
}

async function handleStatusChange(data: any, supabase: any) {
  console.log('Processing status change:', data)

  return new Response(
    JSON.stringify({
      success: true,
      message: 'Status change processed successfully',
      event: 'quote.status_changed',
      quote_id: data.quote_id,
      new_status: data.status,
      processed_at: new Date().toISOString()
    }),
    {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  )
}
