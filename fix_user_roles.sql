-- Fix User Roles and Workspace Ownership
-- Run this in Supabase Dashboard > SQL Editor
-- This ensures all users are workspace owners with proper access to SaaS features

-- 1. Update all 'viewer' role users to 'user' role (except <PERSON> who should be saas_owner)
UPDATE profiles 
SET role = 'user'
WHERE role = 'viewer' 
AND email != '<EMAIL>';

-- 2. Ensure Stephen has saas_owner role
UPDATE profiles 
SET role = 'saas_owner'
WHERE email = '<EMAIL>';

-- 3. Check for users without organizations and create them
DO $$
DECLARE
  user_record RECORD;
  user_org_id UUID;
BEGIN
  FOR user_record IN 
    SELECT p.id, p.email, p.full_name 
    FROM profiles p
    LEFT JOIN organization_members om ON p.id = om.user_id AND om.role = 'owner'
    WHERE om.user_id IS NULL 
    AND p.email != '<EMAIL>'
  LOOP
    -- Create personal organization for users who don't have one
    INSERT INTO organizations (name, slug, subscription_plan, workflow_credits_limit, ai_credits_limit)
    VALUES (
      COALESCE(user_record.full_name, user_record.email) || '''s Workspace',
      'user-' || user_record.id,
      'free',
      100,  -- Free tier workflow credits
      50    -- Free tier AI credits
    )
    ON CONFLICT (slug) DO NOTHING
    RETURNING id INTO user_org_id;

    -- Get organization ID if it already existed
    IF user_org_id IS NULL THEN
      SELECT id INTO user_org_id FROM organizations WHERE slug = 'user-' || user_record.id;
    END IF;

    -- Add user as owner of their organization
    INSERT INTO organization_members (organization_id, user_id, role, joined_at, is_active)
    VALUES (user_org_id, user_record.id, 'owner', NOW(), true)
    ON CONFLICT (organization_id, user_id) DO UPDATE SET 
      role = 'owner',
      is_active = true;

    RAISE NOTICE 'Fixed workspace ownership for user: %', user_record.email;
  END LOOP;
END $$;

-- 4. Verify the changes
SELECT 
  'User Role Summary' as summary,
  role,
  COUNT(*) as user_count
FROM profiles 
GROUP BY role
ORDER BY role;

-- 5. Verify workspace ownership
SELECT 
  'Workspace Ownership Summary' as summary,
  COUNT(DISTINCT p.id) as total_users,
  COUNT(DISTINCT om.user_id) as users_with_workspace,
  COUNT(DISTINCT CASE WHEN om.role = 'owner' THEN om.user_id END) as workspace_owners
FROM profiles p
LEFT JOIN organization_members om ON p.id = om.user_id AND om.is_active = true;

-- 6. Show users without workspaces (should be empty after this script)
SELECT 
  'Users Without Workspaces' as issue,
  p.email,
  p.role
FROM profiles p
LEFT JOIN organization_members om ON p.id = om.user_id AND om.role = 'owner' AND om.is_active = true
WHERE om.user_id IS NULL;

SELECT 'User roles and workspace ownership fixed successfully!' as status;
