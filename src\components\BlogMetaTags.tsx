import React, { useEffect } from 'react'
import { BlogPost } from '@/lib/supabase'

interface BlogMetaTagsProps {
  post: BlogPost
}

const BlogMetaTags: React.FC<BlogMetaTagsProps> = ({ post }) => {
  useEffect(() => {
    // Update page title
    document.title = `${post.title} | MBI Blog`

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', post.excerpt || `Read "${post.title}" on the MBI Blog`)
    } else {
      const newMetaDescription = document.createElement('meta')
      newMetaDescription.name = 'description'
      newMetaDescription.content = post.excerpt || `Read "${post.title}" on the MBI Blog`
      document.head.appendChild(newMetaDescription)
    }

    // Update Open Graph tags
    const updateOrCreateMetaTag = (property: string, content: string) => {
      let metaTag = document.querySelector(`meta[property="${property}"]`)
      if (metaTag) {
        metaTag.setAttribute('content', content)
      } else {
        metaTag = document.createElement('meta')
        metaTag.setAttribute('property', property)
        metaTag.setAttribute('content', content)
        document.head.appendChild(metaTag)
      }
    }

    // Open Graph tags
    updateOrCreateMetaTag('og:title', post.title)
    updateOrCreateMetaTag('og:description', post.excerpt || `Read "${post.title}" on the MBI Blog`)
    updateOrCreateMetaTag('og:type', 'article')
    updateOrCreateMetaTag('og:url', window.location.href)
    
    // Use featured image if available, otherwise use default OG image
    const ogImage = post.featured_image || 'https://site.millennialbusinessinnovations.com/og-image.png'
    updateOrCreateMetaTag('og:image', ogImage)
    updateOrCreateMetaTag('og:image:width', '1200')
    updateOrCreateMetaTag('og:image:height', '630')
    updateOrCreateMetaTag('og:image:alt', post.title)

    // Twitter tags
    updateOrCreateMetaTag('twitter:card', 'summary_large_image')
    updateOrCreateMetaTag('twitter:title', post.title)
    updateOrCreateMetaTag('twitter:description', post.excerpt || `Read "${post.title}" on the MBI Blog`)
    updateOrCreateMetaTag('twitter:image', ogImage)
    updateOrCreateMetaTag('twitter:image:alt', post.title)

    // Article specific tags
    updateOrCreateMetaTag('article:published_time', post.published_at || post.created_at)
    if (post.updated_at !== post.created_at) {
      updateOrCreateMetaTag('article:modified_time', post.updated_at)
    }
    updateOrCreateMetaTag('article:author', post.author?.full_name || 'MBI Team')

    // Cleanup function to restore original meta tags when component unmounts
    return () => {
      document.title = 'Millennial Business Innovations - Transform Your Ideas into Digital Reality'
      
      const originalDescription = 'We help startups and businesses build MVPs, custom websites, landing pages, and SaaS solutions. Transform your ideas into digital reality.'
      const metaDescription = document.querySelector('meta[name="description"]')
      if (metaDescription) {
        metaDescription.setAttribute('content', originalDescription)
      }

      // Restore original OG tags
      updateOrCreateMetaTag('og:title', 'Millennial Business Innovations - Transform Your Ideas into Digital Reality')
      updateOrCreateMetaTag('og:description', originalDescription)
      updateOrCreateMetaTag('og:type', 'website')
      updateOrCreateMetaTag('og:url', 'https://site.millennialbusinessinnovations.com/')
      updateOrCreateMetaTag('og:image', 'https://site.millennialbusinessinnovations.com/og-image.png')
    }
  }, [post])

  return null // This component doesn't render anything
}

export default BlogMetaTags
