-- Newsletter System Migration
-- Creates newsletter subscribers table and workflow triggers

-- Create newsletter subscribers table
CREATE TABLE IF NOT EXISTS newsletter_subscribers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  name TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'unsubscribed', 'bounced')),
  source TEXT DEFAULT 'website' CHECK (source IN ('website', 'blog', 'quote_form', 'manual', 'footer', 'newsletter', 'contact', 'social', 'referral', 'api')),
  subscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  unsubscribed_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}', -- Store additional data like referrer, utm params, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for email lookups
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_email ON newsletter_subscribers(email);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_status ON newsletter_subscribers(status);
CREATE INDEX IF NOT EXISTS idx_newsletter_subscribers_source ON newsletter_subscribers(source);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_newsletter_subscribers_updated_at
  BEFORE UPDATE ON newsletter_subscribers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add newsletter_signup trigger type to workflows if not exists
-- This will be used to trigger workflows when someone subscribes

-- Create function to trigger newsletter workflows
CREATE OR REPLACE FUNCTION trigger_newsletter_workflows()
RETURNS TRIGGER AS $$
BEGIN
  -- Only trigger on new subscriptions (INSERT) or resubscriptions (status change to active)
  IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active') THEN
    
    -- Find and execute workflows with newsletter_signup trigger
    PERFORM pg_notify('workflow_trigger', json_build_object(
      'trigger_type', 'newsletter_signup',
      'subscriber_id', NEW.id,
      'email', NEW.email,
      'name', NEW.name,
      'source', NEW.source,
      'metadata', NEW.metadata,
      'subscribed_at', NEW.subscribed_at
    )::text);
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for newsletter workflows
CREATE TRIGGER newsletter_workflow_trigger
  AFTER INSERT OR UPDATE ON newsletter_subscribers
  FOR EACH ROW
  EXECUTE FUNCTION trigger_newsletter_workflows();

-- RLS Policies for newsletter_subscribers
ALTER TABLE newsletter_subscribers ENABLE ROW LEVEL SECURITY;

-- Allow public to insert (subscribe)
CREATE POLICY "Anyone can subscribe to newsletter" ON newsletter_subscribers
  FOR INSERT WITH CHECK (true);

-- Allow public to update their own subscription (unsubscribe)
CREATE POLICY "Anyone can update their own subscription" ON newsletter_subscribers
  FOR UPDATE USING (true) WITH CHECK (true);

-- Allow public to read (for unsubscribe links)
CREATE POLICY "Anyone can read newsletter subscriptions" ON newsletter_subscribers
  FOR SELECT USING (true);

-- Admins can manage all subscriptions
CREATE POLICY "Admins can manage all newsletter subscriptions" ON newsletter_subscribers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create newsletter campaigns table for tracking email campaigns
CREATE TABLE IF NOT EXISTS newsletter_campaigns (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'sending', 'sent', 'cancelled')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  recipient_count INTEGER DEFAULT 0,
  opened_count INTEGER DEFAULT 0,
  clicked_count INTEGER DEFAULT 0,
  unsubscribed_count INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create trigger for newsletter campaigns updated_at
CREATE TRIGGER update_newsletter_campaigns_updated_at
  BEFORE UPDATE ON newsletter_campaigns
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- RLS for newsletter campaigns
ALTER TABLE newsletter_campaigns ENABLE ROW LEVEL SECURITY;

-- Only admins can manage campaigns
CREATE POLICY "Admins can manage newsletter campaigns" ON newsletter_campaigns
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Create newsletter analytics table
CREATE TABLE IF NOT EXISTS newsletter_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  campaign_id UUID REFERENCES newsletter_campaigns(id) ON DELETE CASCADE,
  subscriber_id UUID REFERENCES newsletter_subscribers(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL CHECK (event_type IN ('sent', 'delivered', 'opened', 'clicked', 'unsubscribed', 'bounced')),
  event_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_newsletter_analytics_campaign ON newsletter_analytics(campaign_id);
CREATE INDEX IF NOT EXISTS idx_newsletter_analytics_subscriber ON newsletter_analytics(subscriber_id);
CREATE INDEX IF NOT EXISTS idx_newsletter_analytics_event_type ON newsletter_analytics(event_type);

-- RLS for newsletter analytics
ALTER TABLE newsletter_analytics ENABLE ROW LEVEL SECURITY;

-- Only admins can view analytics
CREATE POLICY "Admins can view newsletter analytics" ON newsletter_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role IN ('owner', 'super_admin', 'admin')
    )
  );

-- Insert sample welcome email workflow template
-- This will be created as a template that users can activate
INSERT INTO workflows (name, description, is_template, active, nodes, created_by)
SELECT
  'Newsletter Welcome Series',
  'Automated welcome email series for new newsletter subscribers',
  true,
  false,
  '[
    {
      "id": "trigger-1",
      "type": "trigger",
      "position": {"x": 100, "y": 100},
      "data": {
        "triggerType": "newsletter_signup",
        "label": "Newsletter Signup"
      },
      "connections": ["delay-1"]
    },
    {
      "id": "delay-1", 
      "type": "action",
      "position": {"x": 300, "y": 100},
      "data": {
        "actionType": "delay",
        "label": "Wait 5 minutes",
        "config": {
          "delayMs": 300000
        }
      },
      "connections": ["email-1"]
    },
    {
      "id": "email-1",
      "type": "action", 
      "position": {"x": 500, "y": 100},
      "data": {
        "actionType": "send_email",
        "label": "Welcome Email",
        "config": {
          "to": "{{ email }}",
          "subject": "Welcome to MBI Newsletter! 🚀",
          "body": "<h1>Welcome {{ name || \"there\" }}!</h1><p>Thank you for subscribing to the Millennial Business Innovations newsletter.</p><p>You can expect:</p><ul><li>Weekly insights on digital innovation</li><li>Startup success stories</li><li>Exclusive tips and resources</li><li>Early access to our latest tools</li></ul><p>Best regards,<br>The MBI Team</p>"
        }
      },
      "connections": ["delay-2"]
    },
    {
      "id": "delay-2",
      "type": "action",
      "position": {"x": 700, "y": 100}, 
      "data": {
        "actionType": "delay",
        "label": "Wait 3 days",
        "config": {
          "delayMs": 259200000
        }
      },
      "connections": ["email-2"]
    },
    {
      "id": "email-2",
      "type": "action",
      "position": {"x": 900, "y": 100},
      "data": {
        "actionType": "send_email", 
        "label": "Value Email #1",
        "config": {
          "to": "{{ email }}",
          "subject": "5 Mistakes That Kill Startup Success",
          "body": "<h1>Hi {{ name || \"there\" }}!</h1><p>Here are the 5 most common mistakes we see startups make:</p><ol><li>Building without validating the market</li><li>Overcomplicating the MVP</li><li>Ignoring user feedback</li><li>Scaling too early</li><li>Not focusing on retention</li></ol><p>Want to avoid these pitfalls? <a href=\"https://millennialbusinessinnovations.com/services\">Let''s chat about your project</a>.</p><p>Best,<br>The MBI Team</p>"
        }
      },
      "connections": []
    }
  ]'::jsonb,
  (SELECT id FROM auth.users WHERE email = '<EMAIL>' LIMIT 1)
WHERE NOT EXISTS (
  SELECT 1 FROM workflows WHERE name = 'Newsletter Welcome Series' AND is_template = true
);
