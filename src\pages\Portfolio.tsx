import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ExternalLink } from 'lucide-react';
import { ThemeToggle } from '@/components/ThemeToggle';

const Portfolio = () => {
  const allProjects = [
    {
      title: 'Aha-Innovations Platform',
      category: 'SaaS Platform',
      description: 'All-in-one business innovation platform featuring project management, collaboration tools, and analytics dashboard.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://site.aha-innovations.com/',
      tech: ['React', 'Next.js', 'TypeScript'],
      metrics: 'Live Platform',
      url: 'https://site.aha-innovations.com/'
    },
    {
      title: 'RR Twins',
      category: 'Custom Web Solution',
      description: 'Professional twin performers website showcasing their entertainment services, portfolio, and booking system.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://rrtwins.com/',
      tech: ['React', 'Tailwind CSS', 'Vercel'],
      metrics: 'Entertainment Portfolio',
      url: 'https://rrtwins.com/'
    },
    {
      title: 'Denti-Nexus Clinic Suite',
      category: 'Healthcare Platform',
      description: 'Comprehensive dental clinic management system with appointment scheduling, patient records, and billing integration.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://denti-nexus-clinic-suite.vercel.app/',
      tech: ['React', 'Next.js', 'Healthcare APIs'],
      metrics: 'Healthcare Solution',
      url: 'https://denti-nexus-clinic-suite.vercel.app/'
    },
    {
      title: 'Stephen Lovino Portfolio',
      category: 'Personal Portfolio',
      description: 'Professional portfolio website showcasing development skills, projects, and career achievements with modern design.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://www.stephenlovino.com/',
      tech: ['React', 'Portfolio Design', 'Responsive'],
      metrics: 'Personal Brand',
      url: 'https://www.stephenlovino.com/'
    },
    {
      title: 'MBI Corporate Site',
      category: 'Corporate Website',
      description: 'Corporate website for Millennial Business Innovations showcasing services, team, and company achievements.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://site.millennialbusinessinnovations.com/',
      tech: ['React', 'Corporate Design', 'SEO'],
      metrics: 'Corporate Solution',
      url: 'https://site.millennialbusinessinnovations.com/'
    },
    {
      title: 'Undertake PH',
      category: 'Business Website',
      description: 'Corporate website for Philippine-based business services company with modern design and service showcase.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://undertakeph.com/',
      tech: ['React', 'CSS3', 'Responsive Design'],
      metrics: 'Corporate Solution',
      url: 'https://undertakeph.com/'
    },
    {
      title: 'Launchpad Website Craft',
      category: 'Landing Page',
      description: 'High-converting landing page for website development services with modern animations and clear CTAs.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://launchpad-website-craft.vercel.app/',
      tech: ['React', 'Framer Motion', 'Vercel'],
      metrics: 'High Conversion',
      url: 'https://launchpad-website-craft.vercel.app/'
    },
    {
      title: 'Image Source Finder',
      category: 'Web Application',
      description: 'Powerful tool for finding and analyzing image sources with reverse image search capabilities and metadata extraction.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://image-source-finder.vercel.app/',
      tech: ['React', 'API Integration', 'Vercel'],
      metrics: 'Utility Tool',
      url: 'https://image-source-finder.vercel.app/'
    },
    {
      title: 'Timepiece Collection',
      category: 'E-commerce',
      description: 'Elegant watch collection showcase with premium design, product galleries, and sophisticated user experience.',
      image: 'https://image.thum.io/get/width/500/crop/300/https://timepiece.site/',
      tech: ['React', 'E-commerce', 'Premium Design'],
      metrics: 'Luxury Showcase',
      url: 'https://timepiece.site/'
    }
  ];

  const goBack = () => {
    window.history.back();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-black dark:via-gray-900 dark:to-black">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/95 dark:bg-black/95 backdrop-blur-lg border-b border-gray-200 dark:border-white/10">
        <div className="container mx-auto px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                onClick={goBack}
                className="text-gray-900 dark:text-white hover:text-primary transition-colors"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back
              </Button>
              <div className="font-montserrat font-bold text-xl sm:text-2xl text-gray-900 dark:text-white">
                <span className="text-primary">M</span>BI Portfolio
              </div>
            </div>
            <ThemeToggle />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 py-12 sm:py-16">
        <div className="text-center mb-12 sm:mb-16">
          <h1 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Complete <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Portfolio</span>
          </h1>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            Explore our complete collection of projects and the innovative solutions we've delivered for our clients.
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {allProjects.map((project, index) => (
            <Card
              key={index}
              className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg hover:bg-white dark:hover:bg-white/10 transition-all duration-300 overflow-hidden group hover:transform hover:scale-105"
            >
              <div className="relative overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-40 sm:h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute top-3 sm:top-4 left-3 sm:left-4">
                  <span className="bg-primary/80 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-poppins">
                    {project.category}
                  </span>
                </div>
                <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-white/90 hover:bg-white text-gray-900 p-2"
                    onClick={() => window.open(project.url, '_blank')}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              <CardContent className="p-4 sm:p-6 space-y-3 sm:space-y-4">
                <h3 className="font-montserrat font-semibold text-lg sm:text-xl text-gray-900 dark:text-white group-hover:text-primary transition-colors">
                  {project.title}
                </h3>
                <p className="font-poppins text-gray-600 dark:text-gray-300 text-sm">
                  {project.description}
                </p>

                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech, idx) => (
                    <span
                      key={idx}
                      className="bg-accent/20 text-accent px-2 py-1 rounded text-xs font-poppins"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between pt-3 sm:pt-4 border-t border-gray-200 dark:border-white/10">
                  <span className="text-secondary text-xs sm:text-sm font-poppins font-semibold">
                    {project.metrics}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-primary hover:text-primary/80 hover:bg-primary/10 text-xs sm:text-sm"
                    onClick={() => window.open(project.url, '_blank')}
                  >
                    Visit Site
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    </div>
  );
};

export default Portfolio;
