/*! @license DOMPurify 3.2.6 | (c) <PERSON><PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:gt,setPrototypeOf:rt,isFrozen:Yt,getPrototypeOf:jt,getOwnPropertyDescriptor:Xt}=Object;let{freeze:S,seal:y,create:_t}=Object,{apply:xe,construct:Pe}=typeof Reflect<"u"&&Reflect;S||(S=function(n){return n});y||(y=function(n){return n});xe||(xe=function(n,s,r){return n.apply(s,r)});Pe||(Pe=function(n,s){return new n(...s)});const fe=R(Array.prototype.forEach),Vt=R(Array.prototype.lastIndexOf),st=R(Array.prototype.pop),q=R(Array.prototype.push),$t=R(Array.prototype.splice),me=R(String.prototype.toLowerCase),Ie=R(String.prototype.toString),lt=R(String.prototype.match),K=R(String.prototype.replace),qt=R(String.prototype.indexOf),Kt=R(String.prototype.trim),b=R(Object.prototype.hasOwnProperty),A=R(RegExp.prototype.test),Z=Zt(TypeError);function R(o){return function(n){n instanceof RegExp&&(n.lastIndex=0);for(var s=arguments.length,r=new Array(s>1?s-1:0),f=1;f<s;f++)r[f-1]=arguments[f];return xe(o,n,r)}}function Zt(o){return function(){for(var n=arguments.length,s=new Array(n),r=0;r<n;r++)s[r]=arguments[r];return Pe(o,s)}}function l(o,n){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:me;rt&&rt(o,null);let r=n.length;for(;r--;){let f=n[r];if(typeof f=="string"){const O=s(f);O!==f&&(Yt(n)||(n[r]=O),f=O)}o[f]=!0}return o}function Jt(o){for(let n=0;n<o.length;n++)b(o,n)||(o[n]=null);return o}function w(o){const n=_t(null);for(const[s,r]of gt(o))b(o,s)&&(Array.isArray(r)?n[s]=Jt(r):r&&typeof r=="object"&&r.constructor===Object?n[s]=w(r):n[s]=r);return n}function J(o,n){for(;o!==null;){const r=Xt(o,n);if(r){if(r.get)return R(r.get);if(typeof r.value=="function")return R(r.value)}o=jt(o)}function s(){return null}return s}const ct=S(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ne=S(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Me=S(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Qt=S(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ce=S(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),en=S(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),ft=S(["#text"]),ut=S(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),we=S(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),mt=S(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ue=S(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),tn=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),nn=y(/<%[\w\W]*|[\w\W]*%>/gm),on=y(/\$\{[\w\W]*/gm),an=y(/^data-[\-\w.\u00B7-\uFFFF]+$/),rn=y(/^aria-[\-\w]+$/),Et=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),sn=y(/^(?:\w+script|data):/i),ln=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ht=y(/^html$/i),cn=y(/^[a-z][.\w]*(-[.\w]+)+$/i);var pt=Object.freeze({__proto__:null,ARIA_ATTR:rn,ATTR_WHITESPACE:ln,CUSTOM_ELEMENT:cn,DATA_ATTR:an,DOCTYPE_NAME:ht,ERB_EXPR:nn,IS_ALLOWED_URI:Et,IS_SCRIPT_OR_DATA:sn,MUSTACHE_EXPR:tn,TMPLIT_EXPR:on});const Q={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},fn=function(){return typeof window>"u"?null:window},un=function(n,s){if(typeof n!="object"||typeof n.createPolicy!="function")return null;let r=null;const f="data-tt-policy-suffix";s&&s.hasAttribute(f)&&(r=s.getAttribute(f));const O="dompurify"+(r?"#"+r:"");try{return n.createPolicy(O,{createHTML(I){return I},createScriptURL(I){return I}})}catch{return console.warn("TrustedTypes policy "+O+" could not be created."),null}},dt=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function At(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:fn();const n=a=>At(a);if(n.version="3.2.6",n.removed=[],!o||!o.document||o.document.nodeType!==Q.document||!o.Element)return n.isSupported=!1,n;let{document:s}=o;const r=s,f=r.currentScript,{DocumentFragment:O,HTMLTemplateElement:I,Node:P,Element:ee,NodeFilter:G,NamedNodeMap:St=o.NamedNodeMap||o.MozNamedAttrMap,HTMLFormElement:Rt,DOMParser:Lt,trustedTypes:te}=o,B=ee.prototype,Ot=J(B,"cloneNode"),yt=J(B,"remove"),bt=J(B,"nextSibling"),Dt=J(B,"childNodes"),ne=J(B,"parentNode");if(typeof I=="function"){const a=s.createElement("template");a.content&&a.content.ownerDocument&&(s=a.content.ownerDocument)}let E,Y="";const{implementation:pe,createNodeIterator:It,createDocumentFragment:Nt,getElementsByTagName:Mt}=s,{importNode:Ct}=r;let h=dt();n.isSupported=typeof gt=="function"&&typeof ne=="function"&&pe&&pe.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:de,ERB_EXPR:Te,TMPLIT_EXPR:ge,DATA_ATTR:wt,ARIA_ATTR:xt,IS_SCRIPT_OR_DATA:Pt,ATTR_WHITESPACE:ve,CUSTOM_ELEMENT:vt}=pt;let{IS_ALLOWED_URI:ke}=pt,p=null;const Ue=l({},[...ct,...Ne,...Me,...Ce,...ft]);let T=null;const Fe=l({},[...ut,...we,...mt,...ue]);let u=Object.seal(_t(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),j=null,_e=null,He=!0,Ee=!0,ze=!1,We=!0,v=!1,oe=!0,x=!1,he=!1,Ae=!1,k=!1,ie=!1,ae=!1,Ge=!0,Be=!1;const kt="user-content-";let Se=!0,X=!1,U={},F=null;const Ye=l({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let je=null;const Xe=l({},["audio","video","img","source","image","track"]);let Re=null;const Ve=l({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),re="http://www.w3.org/1998/Math/MathML",se="http://www.w3.org/2000/svg",N="http://www.w3.org/1999/xhtml";let H=N,Le=!1,Oe=null;const Ut=l({},[re,se,N],Ie);let le=l({},["mi","mo","mn","ms","mtext"]),ce=l({},["annotation-xml"]);const Ft=l({},["title","style","font","a","script"]);let V=null;const Ht=["application/xhtml+xml","text/html"],zt="text/html";let d=null,z=null;const Wt=s.createElement("form"),$e=function(e){return e instanceof RegExp||e instanceof Function},ye=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(z&&z===e)){if((!e||typeof e!="object")&&(e={}),e=w(e),V=Ht.indexOf(e.PARSER_MEDIA_TYPE)===-1?zt:e.PARSER_MEDIA_TYPE,d=V==="application/xhtml+xml"?Ie:me,p=b(e,"ALLOWED_TAGS")?l({},e.ALLOWED_TAGS,d):Ue,T=b(e,"ALLOWED_ATTR")?l({},e.ALLOWED_ATTR,d):Fe,Oe=b(e,"ALLOWED_NAMESPACES")?l({},e.ALLOWED_NAMESPACES,Ie):Ut,Re=b(e,"ADD_URI_SAFE_ATTR")?l(w(Ve),e.ADD_URI_SAFE_ATTR,d):Ve,je=b(e,"ADD_DATA_URI_TAGS")?l(w(Xe),e.ADD_DATA_URI_TAGS,d):Xe,F=b(e,"FORBID_CONTENTS")?l({},e.FORBID_CONTENTS,d):Ye,j=b(e,"FORBID_TAGS")?l({},e.FORBID_TAGS,d):w({}),_e=b(e,"FORBID_ATTR")?l({},e.FORBID_ATTR,d):w({}),U=b(e,"USE_PROFILES")?e.USE_PROFILES:!1,He=e.ALLOW_ARIA_ATTR!==!1,Ee=e.ALLOW_DATA_ATTR!==!1,ze=e.ALLOW_UNKNOWN_PROTOCOLS||!1,We=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,v=e.SAFE_FOR_TEMPLATES||!1,oe=e.SAFE_FOR_XML!==!1,x=e.WHOLE_DOCUMENT||!1,k=e.RETURN_DOM||!1,ie=e.RETURN_DOM_FRAGMENT||!1,ae=e.RETURN_TRUSTED_TYPE||!1,Ae=e.FORCE_BODY||!1,Ge=e.SANITIZE_DOM!==!1,Be=e.SANITIZE_NAMED_PROPS||!1,Se=e.KEEP_CONTENT!==!1,X=e.IN_PLACE||!1,ke=e.ALLOWED_URI_REGEXP||Et,H=e.NAMESPACE||N,le=e.MATHML_TEXT_INTEGRATION_POINTS||le,ce=e.HTML_INTEGRATION_POINTS||ce,u=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&$e(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(u.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&$e(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(u.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(u.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),v&&(Ee=!1),ie&&(k=!0),U&&(p=l({},ft),T=[],U.html===!0&&(l(p,ct),l(T,ut)),U.svg===!0&&(l(p,Ne),l(T,we),l(T,ue)),U.svgFilters===!0&&(l(p,Me),l(T,we),l(T,ue)),U.mathMl===!0&&(l(p,Ce),l(T,mt),l(T,ue))),e.ADD_TAGS&&(p===Ue&&(p=w(p)),l(p,e.ADD_TAGS,d)),e.ADD_ATTR&&(T===Fe&&(T=w(T)),l(T,e.ADD_ATTR,d)),e.ADD_URI_SAFE_ATTR&&l(Re,e.ADD_URI_SAFE_ATTR,d),e.FORBID_CONTENTS&&(F===Ye&&(F=w(F)),l(F,e.FORBID_CONTENTS,d)),Se&&(p["#text"]=!0),x&&l(p,["html","head","body"]),p.table&&(l(p,["tbody"]),delete j.tbody),e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!="function")throw Z('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Z('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');E=e.TRUSTED_TYPES_POLICY,Y=E.createHTML("")}else E===void 0&&(E=un(te,f)),E!==null&&typeof Y=="string"&&(Y=E.createHTML(""));S&&S(e),z=e}},qe=l({},[...Ne,...Me,...Qt]),Ke=l({},[...Ce,...en]),Gt=function(e){let t=ne(e);(!t||!t.tagName)&&(t={namespaceURI:H,tagName:"template"});const i=me(e.tagName),c=me(t.tagName);return Oe[e.namespaceURI]?e.namespaceURI===se?t.namespaceURI===N?i==="svg":t.namespaceURI===re?i==="svg"&&(c==="annotation-xml"||le[c]):!!qe[i]:e.namespaceURI===re?t.namespaceURI===N?i==="math":t.namespaceURI===se?i==="math"&&ce[c]:!!Ke[i]:e.namespaceURI===N?t.namespaceURI===se&&!ce[c]||t.namespaceURI===re&&!le[c]?!1:!Ke[i]&&(Ft[i]||!qe[i]):!!(V==="application/xhtml+xml"&&Oe[e.namespaceURI]):!1},D=function(e){q(n.removed,{element:e});try{ne(e).removeChild(e)}catch{yt(e)}},W=function(e,t){try{q(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch{q(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),e==="is")if(k||ie)try{D(t)}catch{}else try{t.setAttribute(e,"")}catch{}},Ze=function(e){let t=null,i=null;if(Ae)e="<remove></remove>"+e;else{const m=lt(e,/^[\r\n\t ]+/);i=m&&m[0]}V==="application/xhtml+xml"&&H===N&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const c=E?E.createHTML(e):e;if(H===N)try{t=new Lt().parseFromString(c,V)}catch{}if(!t||!t.documentElement){t=pe.createDocument(H,"template",null);try{t.documentElement.innerHTML=Le?Y:c}catch{}}const g=t.body||t.documentElement;return e&&i&&g.insertBefore(s.createTextNode(i),g.childNodes[0]||null),H===N?Mt.call(t,x?"html":"body")[0]:x?t.documentElement:g},Je=function(e){return It.call(e.ownerDocument||e,e,G.SHOW_ELEMENT|G.SHOW_COMMENT|G.SHOW_TEXT|G.SHOW_PROCESSING_INSTRUCTION|G.SHOW_CDATA_SECTION,null)},be=function(e){return e instanceof Rt&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof St)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},Qe=function(e){return typeof P=="function"&&e instanceof P};function M(a,e,t){fe(a,i=>{i.call(n,e,t,z)})}const et=function(e){let t=null;if(M(h.beforeSanitizeElements,e,null),be(e))return D(e),!0;const i=d(e.nodeName);if(M(h.uponSanitizeElement,e,{tagName:i,allowedTags:p}),oe&&e.hasChildNodes()&&!Qe(e.firstElementChild)&&A(/<[/\w!]/g,e.innerHTML)&&A(/<[/\w!]/g,e.textContent)||e.nodeType===Q.progressingInstruction||oe&&e.nodeType===Q.comment&&A(/<[/\w]/g,e.data))return D(e),!0;if(!p[i]||j[i]){if(!j[i]&&nt(i)&&(u.tagNameCheck instanceof RegExp&&A(u.tagNameCheck,i)||u.tagNameCheck instanceof Function&&u.tagNameCheck(i)))return!1;if(Se&&!F[i]){const c=ne(e)||e.parentNode,g=Dt(e)||e.childNodes;if(g&&c){const m=g.length;for(let L=m-1;L>=0;--L){const C=Ot(g[L],!0);C.__removalCount=(e.__removalCount||0)+1,c.insertBefore(C,bt(e))}}}return D(e),!0}return e instanceof ee&&!Gt(e)||(i==="noscript"||i==="noembed"||i==="noframes")&&A(/<\/no(script|embed|frames)/i,e.innerHTML)?(D(e),!0):(v&&e.nodeType===Q.text&&(t=e.textContent,fe([de,Te,ge],c=>{t=K(t,c," ")}),e.textContent!==t&&(q(n.removed,{element:e.cloneNode()}),e.textContent=t)),M(h.afterSanitizeElements,e,null),!1)},tt=function(e,t,i){if(Ge&&(t==="id"||t==="name")&&(i in s||i in Wt))return!1;if(!(Ee&&!_e[t]&&A(wt,t))){if(!(He&&A(xt,t))){if(!T[t]||_e[t]){if(!(nt(e)&&(u.tagNameCheck instanceof RegExp&&A(u.tagNameCheck,e)||u.tagNameCheck instanceof Function&&u.tagNameCheck(e))&&(u.attributeNameCheck instanceof RegExp&&A(u.attributeNameCheck,t)||u.attributeNameCheck instanceof Function&&u.attributeNameCheck(t))||t==="is"&&u.allowCustomizedBuiltInElements&&(u.tagNameCheck instanceof RegExp&&A(u.tagNameCheck,i)||u.tagNameCheck instanceof Function&&u.tagNameCheck(i))))return!1}else if(!Re[t]){if(!A(ke,K(i,ve,""))){if(!((t==="src"||t==="xlink:href"||t==="href")&&e!=="script"&&qt(i,"data:")===0&&je[e])){if(!(ze&&!A(Pt,K(i,ve,"")))){if(i)return!1}}}}}}return!0},nt=function(e){return e!=="annotation-xml"&&lt(e,vt)},ot=function(e){M(h.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||be(e))return;const i={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:T,forceKeepAttr:void 0};let c=t.length;for(;c--;){const g=t[c],{name:m,namespaceURI:L,value:C}=g,$=d(m),De=C;let _=m==="value"?De:Kt(De);if(i.attrName=$,i.attrValue=_,i.keepAttr=!0,i.forceKeepAttr=void 0,M(h.uponSanitizeAttribute,e,i),_=i.attrValue,Be&&($==="id"||$==="name")&&(W(m,e),_=kt+_),oe&&A(/((--!?|])>)|<\/(style|title)/i,_)){W(m,e);continue}if(i.forceKeepAttr)continue;if(!i.keepAttr){W(m,e);continue}if(!We&&A(/\/>/i,_)){W(m,e);continue}v&&fe([de,Te,ge],at=>{_=K(_,at," ")});const it=d(e.nodeName);if(!tt(it,$,_)){W(m,e);continue}if(E&&typeof te=="object"&&typeof te.getAttributeType=="function"&&!L)switch(te.getAttributeType(it,$)){case"TrustedHTML":{_=E.createHTML(_);break}case"TrustedScriptURL":{_=E.createScriptURL(_);break}}if(_!==De)try{L?e.setAttributeNS(L,m,_):e.setAttribute(m,_),be(e)?D(e):st(n.removed)}catch{W(m,e)}}M(h.afterSanitizeAttributes,e,null)},Bt=function a(e){let t=null;const i=Je(e);for(M(h.beforeSanitizeShadowDOM,e,null);t=i.nextNode();)M(h.uponSanitizeShadowNode,t,null),et(t),ot(t),t.content instanceof O&&a(t.content);M(h.afterSanitizeShadowDOM,e,null)};return n.sanitize=function(a){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=null,i=null,c=null,g=null;if(Le=!a,Le&&(a="<!-->"),typeof a!="string"&&!Qe(a))if(typeof a.toString=="function"){if(a=a.toString(),typeof a!="string")throw Z("dirty is not a string, aborting")}else throw Z("toString is not a function");if(!n.isSupported)return a;if(he||ye(e),n.removed=[],typeof a=="string"&&(X=!1),X){if(a.nodeName){const C=d(a.nodeName);if(!p[C]||j[C])throw Z("root node is forbidden and cannot be sanitized in-place")}}else if(a instanceof P)t=Ze("<!---->"),i=t.ownerDocument.importNode(a,!0),i.nodeType===Q.element&&i.nodeName==="BODY"||i.nodeName==="HTML"?t=i:t.appendChild(i);else{if(!k&&!v&&!x&&a.indexOf("<")===-1)return E&&ae?E.createHTML(a):a;if(t=Ze(a),!t)return k?null:ae?Y:""}t&&Ae&&D(t.firstChild);const m=Je(X?a:t);for(;c=m.nextNode();)et(c),ot(c),c.content instanceof O&&Bt(c.content);if(X)return a;if(k){if(ie)for(g=Nt.call(t.ownerDocument);t.firstChild;)g.appendChild(t.firstChild);else g=t;return(T.shadowroot||T.shadowrootmode)&&(g=Ct.call(r,g,!0)),g}let L=x?t.outerHTML:t.innerHTML;return x&&p["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&A(ht,t.ownerDocument.doctype.name)&&(L="<!DOCTYPE "+t.ownerDocument.doctype.name+`>
`+L),v&&fe([de,Te,ge],C=>{L=K(L,C," ")}),E&&ae?E.createHTML(L):L},n.setConfig=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};ye(a),he=!0},n.clearConfig=function(){z=null,he=!1},n.isValidAttribute=function(a,e,t){z||ye({});const i=d(a),c=d(e);return tt(i,c,t)},n.addHook=function(a,e){typeof e=="function"&&q(h[a],e)},n.removeHook=function(a,e){if(e!==void 0){const t=Vt(h[a],e);return t===-1?void 0:$t(h[a],t,1)[0]}return st(h[a])},n.removeHooks=function(a){h[a]=[]},n.removeAllHooks=function(){h=dt()},n}var mn=At();const pn={images:["image/jpeg","image/jpg","image/png","image/webp","image/gif"],documents:["application/pdf","text/plain"],videos:["video/mp4","video/webm","video/ogg"]},dn={image:5*1024*1024,document:10*1024*1024,video:50*1024*1024},Tt=["exe","bat","cmd","com","pif","scr","vbs","js","jar","php","asp","aspx","jsp"],gn=o=>{var I,P;const n=Tn(o.type),s=dn[n];if(o.size>s)return{isValid:!1,error:`File size exceeds ${Math.round(s/1024/1024)}MB limit`};if(!Object.values(pn).flat().includes(o.type))return{isValid:!1,error:"File type not allowed"};const f=(I=o.name.split(".").pop())==null?void 0:I.toLowerCase();if(!f||Tt.includes(f))return{isValid:!1,error:"File extension not allowed"};const O=o.name.split(".");if(O.length>2){const ee=(P=O[O.length-2])==null?void 0:P.toLowerCase();if(Tt.includes(ee))return{isValid:!1,error:"Suspicious file name detected"}}return{isValid:!0}},Tn=o=>o.startsWith("image/")?"image":o.startsWith("video/")?"video":"document",_n=o=>{var f;const n=(f=o.split(".").pop())==null?void 0:f.toLowerCase(),s=Date.now(),r=Math.random().toString(36).substring(2,15);return`${s}_${r}.${n}`},En=o=>mn.sanitize(o,{ALLOWED_TAGS:["p","br","strong","em","u","s","h1","h2","h3","h4","h5","h6","ul","ol","li","blockquote","a","img","code","pre"],ALLOWED_ATTR:["href","src","alt","title","class"],ALLOW_DATA_ATTR:!1,FORBID_SCRIPT:!0,FORBID_TAGS:["script","object","embed","form","input","iframe"],FORBID_ATTR:["onerror","onload","onclick","onmouseover","style"]}),hn={email:o=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)&&o.length<=254,name:o=>o.length>=1&&o.length<=100&&!/[<>]/.test(o),password:o=>o.length>=8&&o.length<=128,url:o=>{try{const n=new URL(o);return["http:","https:"].includes(n.protocol)}catch{return!1}}};export{pn as ALLOWED_FILE_TYPES,dn as MAX_FILE_SIZES,_n as generateSecureFilename,En as sanitizeHtml,gn as validateFileUpload,hn as validateInput};
