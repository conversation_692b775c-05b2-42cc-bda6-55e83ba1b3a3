-- Add Credit Monitoring and Low Credit Triggers
-- This migration adds functionality to monitor credit usage and trigger workflows when credits are low

-- 1. Create credit monitoring function
CREATE OR REPLACE FUNCTION check_credit_thresholds()
RETURNS TRIGGER AS $$
DECLARE
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id UUID;
    workflow_percentage NUMERIC;
    ai_percentage NUMERIC;
    function_url TEXT;
    response_status INTEGER;
BEGIN
    -- Calculate usage percentages
    workflow_percentage := CASE 
        WHEN NEW.workflow_credits_limit > 0 THEN 
            (NEW.workflow_credits_used::NUMERIC / NEW.workflow_credits_limit::NUMERIC) * 100
        ELSE 0 
    END;
    
    ai_percentage := CASE 
        WHEN NEW.ai_credits_limit > 0 THEN 
            (NEW.ai_credits_used::NUMERIC / NEW.ai_credits_limit::NUMERIC) * 100
        ELSE 0 
    END;

    -- Check if we've crossed the 80% threshold for workflow credits
    IF (OLD.workflow_credits_used::NUMERIC / GREATEST(OLD.workflow_credits_limit, 1)::NUMERIC) * 100 < 80 
       AND workflow_percentage >= 80 THEN
        
        -- Create trigger data for low workflow credits
        trigger_data := jsonb_build_object(
            'organization_id', NEW.id,
            'credit_type', 'workflow',
            'credits_used', NEW.workflow_credits_used,
            'credits_limit', NEW.workflow_credits_limit,
            'usage_percentage', workflow_percentage,
            'threshold', 80,
            'event', 'credits.low_workflow',
            'timestamp', NOW()
        );

        -- Find and execute workflows with low_credits trigger for workflow credits
        FOR workflow_record IN
            SELECT w.* FROM workflows w
            WHERE w.active = true
            AND w.nodes::text LIKE '%"triggerType":"low_credits"%'
            AND w.nodes::text LIKE '%"creditType":"workflow"%'
            AND EXISTS (
                SELECT 1 FROM organization_members om
                WHERE om.organization_id = NEW.id
                AND om.user_id = w.created_by
                AND om.is_active = true
            )
        LOOP
            -- Create execution log
            INSERT INTO workflow_executions (workflow_id, workflow_name, trigger_data, status)
            VALUES (workflow_record.id, workflow_record.name, trigger_data, 'pending')
            RETURNING id INTO execution_id;

            -- Get the Edge Function URL
            SELECT value INTO function_url 
            FROM vault.secrets 
            WHERE name = 'SUPABASE_FUNCTION_URL';
            
            IF function_url IS NULL THEN
                function_url := 'https://your-project.supabase.co/functions/v1/execute-workflow';
            END IF;

            BEGIN
                -- Call Edge Function to execute workflow
                SELECT status INTO response_status
                FROM http((
                    'POST',
                    function_url,
                    ARRAY[
                        http_header('Content-Type', 'application/json'),
                        http_header('Authorization', 'Bearer ' || current_setting('app.jwt_token', true))
                    ],
                    jsonb_build_object(
                        'workflow_id', workflow_record.id,
                        'trigger_data', trigger_data,
                        'execution_id', execution_id
                    )::text
                )::http_request);

                -- Update execution status based on response
                UPDATE workflow_executions 
                SET status = CASE 
                    WHEN response_status BETWEEN 200 AND 299 THEN 'success'
                    ELSE 'failed'
                END
                WHERE id = execution_id;

            EXCEPTION WHEN OTHERS THEN
                -- If Edge Function call fails, update execution status
                UPDATE workflow_executions 
                SET 
                    status = 'failed',
                    error_message = 'Failed to call execution function: ' || SQLERRM
                WHERE id = execution_id;
                
                RAISE NOTICE 'Failed to trigger low credits workflow %: %', workflow_record.name, SQLERRM;
            END;
        END LOOP;
    END IF;

    -- Check if we've crossed the 80% threshold for AI credits
    IF (OLD.ai_credits_used::NUMERIC / GREATEST(OLD.ai_credits_limit, 1)::NUMERIC) * 100 < 80 
       AND ai_percentage >= 80 THEN
        
        -- Create trigger data for low AI credits
        trigger_data := jsonb_build_object(
            'organization_id', NEW.id,
            'credit_type', 'ai',
            'credits_used', NEW.ai_credits_used,
            'credits_limit', NEW.ai_credits_limit,
            'usage_percentage', ai_percentage,
            'threshold', 80,
            'event', 'credits.low_ai',
            'timestamp', NOW()
        );

        -- Find and execute workflows with low_credits trigger for AI credits
        FOR workflow_record IN
            SELECT w.* FROM workflows w
            WHERE w.active = true
            AND w.nodes::text LIKE '%"triggerType":"low_credits"%'
            AND w.nodes::text LIKE '%"creditType":"ai"%'
            AND EXISTS (
                SELECT 1 FROM organization_members om
                WHERE om.organization_id = NEW.id
                AND om.user_id = w.created_by
                AND om.is_active = true
            )
        LOOP
            -- Create execution log
            INSERT INTO workflow_executions (workflow_id, workflow_name, trigger_data, status)
            VALUES (workflow_record.id, workflow_record.name, trigger_data, 'pending')
            RETURNING id INTO execution_id;

            -- Get the Edge Function URL
            SELECT value INTO function_url 
            FROM vault.secrets 
            WHERE name = 'SUPABASE_FUNCTION_URL';
            
            IF function_url IS NULL THEN
                function_url := 'https://your-project.supabase.co/functions/v1/execute-workflow';
            END IF;

            BEGIN
                -- Call Edge Function to execute workflow
                SELECT status INTO response_status
                FROM http((
                    'POST',
                    function_url,
                    ARRAY[
                        http_header('Content-Type', 'application/json'),
                        http_header('Authorization', 'Bearer ' || current_setting('app.jwt_token', true))
                    ],
                    jsonb_build_object(
                        'workflow_id', workflow_record.id,
                        'trigger_data', trigger_data,
                        'execution_id', execution_id
                    )::text
                )::http_request);

                -- Update execution status based on response
                UPDATE workflow_executions 
                SET status = CASE 
                    WHEN response_status BETWEEN 200 AND 299 THEN 'success'
                    ELSE 'failed'
                END
                WHERE id = execution_id;

            EXCEPTION WHEN OTHERS THEN
                -- If Edge Function call fails, update execution status
                UPDATE workflow_executions 
                SET 
                    status = 'failed',
                    error_message = 'Failed to call execution function: ' || SQLERRM
                WHERE id = execution_id;
                
                RAISE NOTICE 'Failed to trigger low AI credits workflow %: %', workflow_record.name, SQLERRM;
            END;
        END LOOP;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 2. Create trigger for credit monitoring
CREATE TRIGGER monitor_credit_usage
    AFTER UPDATE ON organizations
    FOR EACH ROW
    WHEN (OLD.workflow_credits_used IS DISTINCT FROM NEW.workflow_credits_used 
          OR OLD.ai_credits_used IS DISTINCT FROM NEW.ai_credits_used)
    EXECUTE FUNCTION check_credit_thresholds();

-- 3. Create system notifications table for internal notifications
CREATE TABLE IF NOT EXISTS system_notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    notification_type TEXT NOT NULL, -- 'low_credits', 'system_update', 'maintenance', etc.
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- 4. Enable RLS on system_notifications
ALTER TABLE system_notifications ENABLE ROW LEVEL SECURITY;

-- 5. Create RLS policies for system_notifications
CREATE POLICY "Users can view their own notifications" ON system_notifications
    FOR SELECT USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = system_notifications.organization_id
            AND om.user_id = auth.uid()
            AND om.is_active = true
        )
    );

CREATE POLICY "System can create notifications" ON system_notifications
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" ON system_notifications
    FOR UPDATE USING (
        auth.uid() = user_id OR
        EXISTS (
            SELECT 1 FROM organization_members om
            WHERE om.organization_id = system_notifications.organization_id
            AND om.user_id = auth.uid()
            AND om.is_active = true
        )
    );

-- 6. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_system_notifications_user_id ON system_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_system_notifications_org_id ON system_notifications(organization_id);
CREATE INDEX IF NOT EXISTS idx_system_notifications_type ON system_notifications(notification_type);
CREATE INDEX IF NOT EXISTS idx_system_notifications_read ON system_notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_system_notifications_created ON system_notifications(created_at);
