import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  getEmailIntegrations,
  deleteEmailIntegration,
  updateEmailIntegration,
  testEmailIntegration,
  type EmailIntegration
} from '@/lib/supabase'
import { toast } from 'sonner'
import {
  Plus,
  Mail,
  Settings,
  Trash2,
  TestTube,
  Star,
  StarOff,
  Loader2
} from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import EmailIntegrationForm from '@/components/EmailIntegrationForm'
import AdminLayout from '@/components/layout/AdminLayout'

const EmailIntegrations: React.FC = () => {
  const [integrations, setIntegrations] = useState<EmailIntegration[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingIntegration, setEditingIntegration] = useState<EmailIntegration | null>(null)
  const [testingId, setTestingId] = useState<string | null>(null)
  const [testEmail, setTestEmail] = useState('')
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [testingIntegrationId, setTestingIntegrationId] = useState<string | null>(null)

  useEffect(() => {
    loadIntegrations()
  }, [])

  const loadIntegrations = async () => {
    try {
      const { data, error } = await getEmailIntegrations()
      if (error) {
        toast.error('Failed to load email integrations')
        console.error('Error loading integrations:', error)
      } else {
        setIntegrations(data || [])
      }
    } catch (error) {
      toast.error('Failed to load email integrations')
      console.error('Error loading integrations:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this email integration?')) {
      return
    }

    try {
      const { error } = await deleteEmailIntegration(id)
      if (error) {
        toast.error('Failed to delete integration')
      } else {
        toast.success('Integration deleted successfully')
        loadIntegrations()
      }
    } catch (error) {
      toast.error('Failed to delete integration')
    }
  }

  const handleSetDefault = async (id: string) => {
    try {
      const { error } = await updateEmailIntegration(id, { is_default: true })
      if (error) {
        toast.error('Failed to set as default')
      } else {
        toast.success('Set as default integration')
        loadIntegrations()
      }
    } catch (error) {
      toast.error('Failed to set as default')
    }
  }

  const handleTest = async () => {
    if (!testingIntegrationId || !testEmail) {
      toast.error('Please enter a test email address')
      return
    }

    setTestingId(testingIntegrationId)
    try {
      const result = await testEmailIntegration(testingIntegrationId, testEmail)
      console.log('Test email result:', result)

      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message || 'Test email failed')
        console.error('Test email error:', result.message)
      }
    } catch (error) {
      console.error('Test email exception:', error)
      toast.error('Test email failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setTestingId(null)
      setShowTestDialog(false)
      setTestEmail('')
      setTestingIntegrationId(null)
    }
  }

  const openTestDialog = (integrationId: string) => {
    setTestingIntegrationId(integrationId)
    setShowTestDialog(true)
  }

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'gmail':
        return '📧'
      case 'outlook':
        return '📮'
      case 'resend':
        return '🚀'
      case 'postmark':
        return '📬'
      case 'sendgrid':
        return '📨'
      case 'mailgun':
        return '🔫'
      case 'smtp':
        return '⚙️'
      default:
        return '📧'
    }
  }

  const getProviderColor = (provider: string) => {
    switch (provider) {
      case 'gmail':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'outlook':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'resend':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'postmark':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      case 'sendgrid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'mailgun':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200'
      case 'smtp':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <AdminLayout
      title="Email Integrations"
      subtitle="Connect your email services to send automated emails from workflows"
      actions={
        <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Integration
        </Button>
      }
    >
      <div className="space-y-6">
        {/* Provider Status Notice */}
        <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-blue-900 dark:text-blue-100">
                  Email Provider Status
                </h3>
                <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <p><strong>✅ Fully Functional:</strong> All providers (Gmail, Outlook, SMTP, Resend, Postmark, SendGrid, Mailgun)</p>
                  <p><strong>📧 SMTP Providers:</strong> Gmail, Outlook, and Custom SMTP now use server-side implementation</p>
                  <p><strong>🌐 API Providers:</strong> Resend, Postmark, SendGrid, and Mailgun use direct API calls</p>
                  <p>All integrations are ready for testing and production use!</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

      {integrations.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Mail className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              No Email Integrations
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
              Connect your email service to start sending automated emails from your workflows.
            </p>
            <Button onClick={() => setShowForm(true)} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Your First Integration
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {integrations.map((integration) => (
            <Card key={integration.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{getProviderIcon(integration.provider)}</span>
                    <div>
                      <CardTitle className="text-lg">{integration.name}</CardTitle>
                      <Badge className={`mt-1 ${getProviderColor(integration.provider)}`}>
                        {integration.provider.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  {integration.is_default && (
                    <Star className="h-5 w-5 text-yellow-500 fill-current" />
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${integration.is_active ? 'bg-green-500' : 'bg-red-500'}`} />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {integration.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openTestDialog(integration.id)}
                    disabled={testingId === integration.id}
                    className="flex-1"
                  >
                    {testingId === integration.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <TestTube className="h-4 w-4" />
                    )}
                    Test
                  </Button>
                  
                  {!integration.is_default && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleSetDefault(integration.id)}
                    >
                      <StarOff className="h-4 w-4" />
                    </Button>
                  )}
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setEditingIntegration(integration)
                      setShowForm(true)
                    }}
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(integration.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Integration Dialog */}
      <Dialog open={showForm} onOpenChange={(open) => {
        setShowForm(open)
        if (!open) {
          setEditingIntegration(null)
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingIntegration ? 'Edit Email Integration' : 'Add Email Integration'}
            </DialogTitle>
          </DialogHeader>
          <EmailIntegrationForm
            integration={editingIntegration}
            onSuccess={() => {
              setShowForm(false)
              setEditingIntegration(null)
              loadIntegrations()
            }}
            onCancel={() => {
              setShowForm(false)
              setEditingIntegration(null)
            }}
          />
        </DialogContent>
      </Dialog>

      {/* Test Email Dialog */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Test Email Integration</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="test-email">Test Email Address</Label>
              <Input
                id="test-email"
                type="email"
                placeholder="<EMAIL>"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button variant="outline" onClick={() => setShowTestDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleTest} disabled={testingId !== null}>
                {testingId ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Send Test Email
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      </div>
    </AdminLayout>
  )
}

export default EmailIntegrations
