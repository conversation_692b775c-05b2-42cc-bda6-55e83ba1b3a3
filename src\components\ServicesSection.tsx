
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CalendarModal from './CalendarModal';
import ServiceIcon from './ServiceIcon';

const ServicesSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const services = [
    {
      icon: 'mvp',
      title: 'MVP Development',
      description: 'Rapidly prototype and validate your ideas with a Minimum Viable Product (MVP). We help you launch quickly and iterate based on real user feedback.',
      features: ['Rapid prototyping', 'User validation', 'Iterative development', 'Market testing']
    },
    {
      icon: 'web',
      title: 'Custom Web Solutions',
      description: 'Build bespoke websites tailored to your unique business needs. From design to development, we handle it all.',
      features: ['Custom design', 'Responsive layouts', 'Modern frameworks', 'Performance optimized']
    },
    {
      icon: 'landing',
      title: 'Landing Pages',
      description: 'Create high-converting landing pages that capture leads and drive conversions. Our designs are optimized for performance and engagement.',
      features: ['Conversion focused', 'A/B testing', 'Mobile optimized', 'Fast loading']
    },
    {
      icon: 'cloud',
      title: 'SaaS Platforms',
      description: 'Develop scalable Software-as-a-Service (SaaS) solutions that meet your business requirements. We build robust, secure, and user-friendly platforms.',
      features: ['Scalable architecture', 'Security first', 'User management', 'API integration']
    },
    {
      icon: 'strategy',
      title: 'Digital Strategy Consulting',
      description: 'Need guidance on how to leverage technology for growth? Our experts provide strategic consulting to help you navigate the digital landscape.',
      features: ['Strategic planning', 'Technology roadmap', 'Growth analysis', 'Best practices']
    },
    {
      icon: 'support',
      title: 'Technical Support',
      description: 'Ongoing technical support and maintenance to ensure your digital solutions continue to perform at their best.',
      features: ['24/7 monitoring', 'Regular updates', 'Bug fixes', 'Performance optimization']
    }
  ];

  return (
    <section id="services" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-teal-50 via-cyan-50 to-blue-50 dark:from-gray-900 dark:via-cyan-900/10 dark:to-blue-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-r from-cyan-300/20 to-blue-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-60 h-60 bg-gradient-to-r from-blue-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-teal-300/10 to-cyan-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Our <span className="text-transparent bg-gradient-to-r from-cyan-500 to-blue-500 bg-clip-text">Services</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4 sm:px-0">
            We offer a comprehensive range of digital services to help your business thrive in the modern marketplace.
          </p>
        </div>

        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
          {services.map((service, index) => (
            <Card
              key={index}
              className="bg-white/30 dark:bg-white/10 border border-white/40 dark:border-white/20 backdrop-blur-lg hover:bg-white/40 dark:hover:bg-white/15 transition-all duration-300 hover:transform hover:scale-105 group shadow-xl hover:shadow-2xl"
            >
              <CardHeader className="text-center px-4 sm:px-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 bg-gradient-to-br from-cyan-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg group-hover:animate-float">
                  <ServiceIcon type={service.icon} className="w-8 h-8 sm:w-10 sm:h-10" />
                </div>
                <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-cyan-500 group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                  {service.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 px-4 sm:px-6">
                <p className="font-poppins text-gray-600 dark:text-gray-300 text-center text-sm sm:text-base">
                  {service.description}
                </p>
                <ul className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>

              </CardContent>
            </Card>
          ))}
        </div>

        {/* Single CTA Section */}
        <div className="text-center mt-12 sm:mt-16">
          <div className="max-w-2xl mx-auto mb-8">
            <h3 className="font-montserrat font-bold text-2xl sm:text-3xl text-gray-900 dark:text-white mb-4">
              Ready to Transform Your Business?
            </h3>
            <p className="font-poppins text-gray-600 dark:text-gray-300 text-base sm:text-lg">
              Let's discuss how our services can help you achieve your goals. Book a free consultation today.
            </p>
          </div>
          <Button
            onClick={() => setIsCalendarOpen(true)}
            size="lg"
            className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white font-semibold px-8 py-3 text-lg transition-all duration-300 transform hover:scale-105 backdrop-blur-md border border-white/20 shadow-xl hover:shadow-2xl"
          >
            Book a Free Consultation
          </Button>
        </div>
      </div>

      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />
    </section>
  );
};

export default ServicesSection;
