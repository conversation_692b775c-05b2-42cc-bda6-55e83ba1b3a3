import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import TurnstileWidget from './TurnstileWidget'
import { validateInput, rateLimiter } from '@/lib/security'
import { toast } from 'sonner'
import { Loader2, Send, Shield } from 'lucide-react'

interface ContactFormData {
  name: string
  email: string
  company: string
  message: string
  budget: string
  timeline: string
}

const SecureContactForm: React.FC = () => {
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    company: '',
    message: '',
    budget: '',
    timeline: ''
  })
  
  const [turnstileToken, setTurnstileToken] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Partial<ContactFormData>>({})

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {}

    if (!validateInput.name(formData.name)) {
      newErrors.name = 'Please enter a valid name (1-100 characters)'
    }

    if (!validateInput.email(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (formData.message.length < 10 || formData.message.length > 2000) {
      newErrors.message = 'Message must be between 10 and 2000 characters'
    }

    if (formData.company.length > 100) {
      newErrors.company = 'Company name must be less than 100 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Rate limiting check
    const clientId = `${navigator.userAgent}_${window.location.hostname}`
    if (!rateLimiter.isAllowed(clientId, 3, 300000)) { // 3 requests per 5 minutes
      toast.error('Too many requests. Please wait before submitting again.')
      return
    }

    if (!validateForm()) {
      toast.error('Please fix the errors in the form')
      return
    }

    if (!turnstileToken) {
      toast.error('Please complete the security verification')
      return
    }

    setIsSubmitting(true)

    try {
      // Here you would typically send to your backend API
      // For now, we'll simulate the submission
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          turnstileToken,
          timestamp: new Date().toISOString()
        })
      })

      if (response.ok) {
        toast.success('Message sent successfully! We\'ll get back to you within 24 hours.')
        
        // Reset form
        setFormData({
          name: '',
          email: '',
          company: '',
          message: '',
          budget: '',
          timeline: ''
        })
        setTurnstileToken('')
      } else {
        throw new Error('Failed to send message')
      }
    } catch (error) {
      console.error('Contact form error:', error)
      toast.error('Failed to send message. Please try again or contact us directly.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 backdrop-blur-lg">
      <CardHeader className="p-4 sm:p-6">
        <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white flex items-center gap-2">
          <Shield className="h-5 w-5 text-primary" />
          Secure Contact Form
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Protected by advanced security measures
        </p>
      </CardHeader>
      
      <CardContent className="p-4 sm:p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={errors.name ? 'border-red-500' : ''}
                placeholder="Your full name"
                maxLength={100}
                required
              />
              {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={errors.email ? 'border-red-500' : ''}
                placeholder="<EMAIL>"
                maxLength={254}
                required
              />
              {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              type="text"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className={errors.company ? 'border-red-500' : ''}
              placeholder="Your company name"
              maxLength={100}
            />
            {errors.company && <p className="text-sm text-red-500">{errors.company}</p>}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="budget">Project Budget</Label>
              <select
                id="budget"
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="">Select budget range</option>
                <option value="10k-25k">$10,000 - $25,000</option>
                <option value="25k-50k">$25,000 - $50,000</option>
                <option value="50k-100k">$50,000 - $100,000</option>
                <option value="100k+">$100,000+</option>
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeline">Timeline</Label>
              <select
                id="timeline"
                value={formData.timeline}
                onChange={(e) => handleInputChange('timeline', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="">Select timeline</option>
                <option value="asap">ASAP</option>
                <option value="1-3months">1-3 months</option>
                <option value="3-6months">3-6 months</option>
                <option value="6months+">6+ months</option>
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="message">Project Details *</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              className={errors.message ? 'border-red-500' : ''}
              placeholder="Tell us about your project, goals, and requirements..."
              rows={5}
              maxLength={2000}
              required
            />
            <div className="flex justify-between text-sm text-gray-500">
              {errors.message && <span className="text-red-500">{errors.message}</span>}
              <span className="ml-auto">{formData.message.length}/2000</span>
            </div>
          </div>

          {/* Turnstile CAPTCHA */}
          <div className="space-y-2">
            <Label>Security Verification</Label>
            <TurnstileWidget
              onVerify={setTurnstileToken}
              onError={() => {
                setTurnstileToken('')
                toast.error('Security verification failed. Please try again.')
              }}
              onExpire={() => {
                setTurnstileToken('')
                toast.warning('Security verification expired. Please verify again.')
              }}
            />
          </div>

          <Button
            type="submit"
            disabled={isSubmitting || !turnstileToken}
            className="w-full bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-semibold py-3"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending Message...
              </>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Send Secure Message
              </>
            )}
          </Button>

          <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
            This form is protected by Cloudflare Turnstile and our advanced security measures.
            Your data is encrypted and handled securely.
          </p>
        </form>
      </CardContent>
    </Card>
  )
}

export default SecureContactForm
