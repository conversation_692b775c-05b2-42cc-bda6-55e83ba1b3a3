import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { workflow_id, execution_id, trigger_data } = await req.json()

    console.log('Executing workflow:', { workflow_id, execution_id })

    // Get the workflow
    const { data: workflow, error: workflowError } = await supabase
      .from('workflows')
      .select('*')
      .eq('id', workflow_id)
      .single()

    if (workflowError || !workflow) {
      throw new Error(`Workflow not found: ${workflowError?.message}`)
    }

    const startTime = Date.now()

    try {
      // Execute the workflow
      await executeWorkflowNodes(workflow, trigger_data, supabase)

      const executionTime = Date.now() - startTime

      // Update execution status to success
      await supabase
        .from('workflow_executions')
        .update({
          status: 'success',
          execution_time_ms: executionTime
        })
        .eq('id', execution_id)

      console.log('Workflow executed successfully:', workflow.name)

      return new Response(
        JSON.stringify({
          success: true,
          message: 'Workflow executed successfully',
          execution_time_ms: executionTime
        }),
        {
          status: 200,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )

    } catch (executionError) {
      const executionTime = Date.now() - startTime

      // Update execution status to failed
      await supabase
        .from('workflow_executions')
        .update({
          status: 'failed',
          error_message: executionError.message,
          execution_time_ms: executionTime
        })
        .eq('id', execution_id)

      console.error('Workflow execution failed:', executionError)

      return new Response(
        JSON.stringify({
          success: false,
          error: executionError.message,
          execution_time_ms: executionTime
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      )
    }

  } catch (error) {
    console.error('Error in execute-workflow function:', error)
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})

async function executeWorkflowNodes(workflow: any, triggerData: any, supabase: any) {
  const nodes = workflow.nodes || []
  
  // Find trigger nodes (starting points)
  const triggerNodes = nodes.filter((node: any) => node.type === 'trigger')

  if (triggerNodes.length === 0) {
    throw new Error('No trigger nodes found in workflow')
  }

  // Process each trigger node's flow
  for (const triggerNode of triggerNodes) {
    await processNodeFlow(triggerNode, nodes, triggerData, supabase)
  }
}

async function processNodeFlow(currentNode: any, allNodes: any[], triggerData: any, supabase: any) {
  // Execute current node
  await executeNode(currentNode, triggerData, supabase)

  // Process connected nodes
  if (currentNode.connections && currentNode.connections.length > 0) {
    for (const connectionId of currentNode.connections) {
      const nextNode = allNodes.find(node => node.id === connectionId)
      if (nextNode) {
        await processNodeFlow(nextNode, allNodes, triggerData, supabase)
      }
    }
  }
}

async function executeNode(node: any, triggerData: any, supabase: any) {
  console.log(`Executing node: ${node.type} - ${node.data?.actionType || node.data?.triggerType}`)

  switch (node.type) {
    case 'trigger':
      // Triggers don't need execution, they just start the flow
      console.log(`Trigger executed: ${node.data.triggerType}`)
      break

    case 'condition':
      // TODO: Implement condition logic
      console.log(`Condition executed: ${node.data.conditionType}`)
      break

    case 'action':
      await executeAction(node, triggerData, supabase)
      break

    default:
      console.warn(`Unknown node type: ${node.type}`)
  }
}

async function executeAction(node: any, triggerData: any, supabase: any) {
  const actionType = node.data?.actionType

  switch (actionType) {
    case 'send_email':
      await executeSendEmailAction(node, triggerData, supabase)
      break

    case 'send_notification':
      await executeSendNotificationAction(node, triggerData, supabase)
      break

    case 'auto_response':
      await executeAutoResponseAction(node, triggerData, supabase)
      break

    case 'webhook':
      await executeWebhookAction(node, triggerData)
      break

    case 'delay':
      await executeDelayAction(node)
      break

    default:
      console.warn(`Unknown action type: ${actionType}`)
  }
}

async function executeSendEmailAction(node: any, triggerData: any, supabase: any) {
  const config = node.data.config || {}
  
  // Get email integration
  const { data: integration } = await supabase
    .from('email_integrations')
    .select('*')
    .eq('id', config.integrationId)
    .single()

  if (!integration) {
    throw new Error('Email integration not found')
  }

  // Render email content with liquid templates
  const renderedSubject = renderLiquidTemplate(config.subject || '', triggerData)
  const renderedBody = renderLiquidTemplate(config.body || '', triggerData)
  const toEmail = renderLiquidTemplate(config.to || '', triggerData)

  console.log('Sending email:', { to: toEmail, subject: renderedSubject })

  // Call the send-email function
  const response = await fetch(`${Deno.env.get('SUPABASE_URL')}/functions/v1/send-email`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`
    },
    body: JSON.stringify({
      integrationId: integration.id,
      to: toEmail,
      subject: renderedSubject,
      body: renderedBody
    })
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Failed to send email: ${error}`)
  }

  console.log('Email sent successfully')
}

async function executeWebhookAction(node: any, triggerData: any) {
  const config = node.data.config || {}
  
  const response = await fetch(config.url, {
    method: config.method || 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...config.headers
    },
    body: JSON.stringify(triggerData)
  })

  if (!response.ok) {
    throw new Error(`Webhook failed: ${response.status} ${response.statusText}`)
  }

  console.log('Webhook executed successfully')
}

async function executeDelayAction(node: any) {
  const config = node.data.config || {}
  const delayMs = config.delayMs || 1000

  console.log(`Delaying for ${delayMs}ms`)
  await new Promise(resolve => setTimeout(resolve, delayMs))
}

function renderLiquidTemplate(template: string, data: any): string {
  if (!template) return ''
  
  // Simple liquid template rendering
  let rendered = template
  
  // Replace {{ variable }} patterns
  rendered = rendered.replace(/\{\{\s*([^}]+)\s*\}\}/g, (match, path) => {
    const value = getNestedValue(data, path.trim())
    return value !== undefined ? String(value) : match
  })
  
  return rendered
}

function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

async function executeSendNotificationAction(node: any, triggerData: any, supabase: any) {
  const config = node.data.config || {}

  try {
    // Determine notification recipients based on trigger type
    let recipients: any[] = []

    if (triggerData.event?.startsWith('blog.')) {
      // For blog events, notify blog owner and relevant users
      if (triggerData.event === 'blog.comment_created') {
        // Get blog post author
        const { data: blogPost } = await supabase
          .from('blog_posts')
          .select('author_id, title')
          .eq('id', triggerData.post_id)
          .single()

        if (blogPost?.author_id) {
          recipients.push({
            user_id: blogPost.author_id,
            type: 'blog_comment',
            title: 'New Comment on Your Blog Post',
            message: `Someone commented on your blog post "${blogPost.title}"`
          })
        }

        // If it's a reply, also notify the parent comment author
        if (triggerData.parent_id) {
          const { data: parentComment } = await supabase
            .from('blog_comments')
            .select('user_id')
            .eq('id', triggerData.parent_id)
            .single()

          if (parentComment?.user_id && parentComment.user_id !== blogPost?.author_id) {
            recipients.push({
              user_id: parentComment.user_id,
              type: 'comment_reply',
              title: 'Someone Replied to Your Comment',
              message: `Someone replied to your comment on "${blogPost.title}"`
            })
          }
        }
      } else if (triggerData.event === 'blog.reaction_created') {
        // Get blog post author
        const { data: blogPost } = await supabase
          .from('blog_posts')
          .select('author_id, title')
          .eq('id', triggerData.post_id)
          .single()

        if (blogPost?.author_id && blogPost.author_id !== triggerData.user_id) {
          recipients.push({
            user_id: blogPost.author_id,
            type: 'blog_reaction',
            title: 'Someone Reacted to Your Blog Post',
            message: `Someone ${triggerData.reaction_type}d your blog post "${blogPost.title}"`
          })
        }
      }
    } else if (triggerData.event?.startsWith('credits.')) {
      // For credit events, notify organization members
      const { data: orgMembers } = await supabase
        .from('organization_members')
        .select('user_id')
        .eq('organization_id', triggerData.organization_id)
        .eq('is_active', true)

      if (orgMembers) {
        recipients = orgMembers.map(member => ({
          user_id: member.user_id,
          type: 'low_credits',
          title: `Low ${triggerData.credit_type} Credits`,
          message: `You have used ${triggerData.usage_percentage}% of your ${triggerData.credit_type} credits`
        }))
      }
    }

    // Create notifications in database
    for (const recipient of recipients) {
      await supabase
        .from('system_notifications')
        .insert({
          user_id: recipient.user_id,
          organization_id: triggerData.organization_id,
          notification_type: recipient.type,
          title: recipient.title,
          message: recipient.message,
          metadata: {
            trigger_data: triggerData,
            workflow_config: config
          }
        })
    }

    console.log(`Sent ${recipients.length} notifications`)
  } catch (error) {
    console.error('Error sending notifications:', error)
    throw error
  }
}

async function executeAutoResponseAction(node: any, triggerData: any, supabase: any) {
  const config = node.data.config || {}

  try {
    // Only respond to blog comments for now
    if (triggerData.event === 'blog.comment_created') {
      const responseTemplate = config.responseTemplate || 'Thank you for your comment!'

      // Replace variables in template
      let responseContent = responseTemplate
        .replace(/\{\{\s*commenter_name\s*\}\}/g, triggerData.guest_name || 'there')
        .replace(/\{\{\s*post_title\s*\}\}/g, triggerData.post_title || 'the blog post')
        .replace(/\{\{\s*current_date\s*\}\}/g, new Date().toLocaleDateString())
        .replace(/\{\{\s*current_time\s*\}\}/g, new Date().toLocaleTimeString())

      // Get blog post details for context
      const { data: blogPost } = await supabase
        .from('blog_posts')
        .select('title, author_id')
        .eq('id', triggerData.post_id)
        .single()

      if (blogPost) {
        responseContent = responseContent.replace(/\{\{\s*post_title\s*\}\}/g, blogPost.title)

        // Only auto-respond if the comment is not from the blog author
        if (triggerData.user_id !== blogPost.author_id) {
          // Create auto-response comment
          await supabase
            .from('blog_comments')
            .insert({
              post_id: triggerData.post_id,
              parent_id: triggerData.comment_id,
              content: responseContent,
              user_id: blogPost.author_id, // Response from blog author
              is_anonymous: false
            })

          console.log('Auto-response comment created')
        }
      }
    }
  } catch (error) {
    console.error('Error creating auto-response:', error)
    throw error
  }
}
