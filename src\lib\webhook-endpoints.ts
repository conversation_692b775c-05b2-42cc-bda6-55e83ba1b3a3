// Webhook Endpoints for External Integrations (GHL, Zapier, etc.)
import { supabase } from './supabase'

export interface WebhookPayload {
  event: 'quote.created' | 'quote.updated' | 'quote.status_changed'
  timestamp: string
  data: {
    quote_id: string
    name: string
    email: string
    phone?: string
    company?: string
    project_type: string
    industry: string
    budget: string
    timeline: string
    key_features: string[]
    integrations: string[]
    status: string
    wants_account: boolean
    created_at: string
    updated_at: string
  }
  previous_data?: any // For update events
}

// Generate webhook URLs for external services
export const generateWebhookUrls = () => {
  const baseUrl = window.location.origin
  
  return {
    // GHL-friendly endpoints
    new_quote: `${baseUrl}/api/webhooks/quote/created`,
    quote_updated: `${baseUrl}/api/webhooks/quote/updated`, 
    status_changed: `${baseUrl}/api/webhooks/quote/status-changed`,
    
    // Generic webhook endpoint
    generic: `${baseUrl}/api/webhooks/automation`,
    
    // Zapier-friendly endpoint
    zapier: `${baseUrl}/api/webhooks/zapier/quote`,
    
    // Slack-friendly endpoint  
    slack: `${baseUrl}/api/webhooks/slack/notification`
  }
}

// Create standardized webhook payload
export const createWebhookPayload = (
  event: WebhookPayload['event'],
  quoteData: any,
  previousData?: any
): WebhookPayload => {
  return {
    event,
    timestamp: new Date().toISOString(),
    data: {
      quote_id: quoteData.id,
      name: quoteData.name,
      email: quoteData.email,
      phone: quoteData.phone,
      company: quoteData.company,
      project_type: quoteData.project_type,
      industry: quoteData.industry,
      budget: quoteData.budget,
      timeline: quoteData.timeline,
      key_features: quoteData.key_features || [],
      integrations: quoteData.integrations || [],
      status: quoteData.status,
      wants_account: quoteData.wants_account || false,
      created_at: quoteData.created_at,
      updated_at: quoteData.updated_at
    },
    previous_data: previousData
  }
}

// Send webhook to external URL
export const sendWebhook = async (
  url: string,
  payload: WebhookPayload,
  headers: Record<string, string> = {}
): Promise<{ success: boolean; response?: any; error?: string }> => {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MBI-Automation/1.0',
        ...headers
      },
      body: JSON.stringify(payload)
    })

    const responseData = await response.text()
    
    return {
      success: response.ok,
      response: {
        status: response.status,
        statusText: response.statusText,
        body: responseData,
        headers: Object.fromEntries(response.headers.entries())
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Log webhook execution
export const logWebhookExecution = async (
  ruleId: string,
  quoteId: string,
  actionType: string,
  url: string,
  payload: WebhookPayload,
  result: { success: boolean; response?: any; error?: string }
) => {
  try {
    await supabase.from('automation_logs').insert({
      rule_id: ruleId,
      quote_id: quoteId,
      trigger_type: payload.event,
      action_type: actionType,
      action_config: { url, method: 'POST' },
      rendered_payload: JSON.stringify(payload),
      request_url: url,
      request_method: 'POST',
      request_headers: { 'Content-Type': 'application/json' },
      response_status: result.response?.status,
      response_body: result.response?.body,
      response_headers: result.response?.headers,
      status: result.success ? 'success' : 'failed',
      error_message: result.error,
      completed_at: new Date().toISOString(),
      duration_ms: 0 // TODO: Add timing
    })
  } catch (error) {
    console.error('Failed to log webhook execution:', error)
  }
}

// Execute automation rule
export const executeAutomationRule = async (
  rule: any,
  quoteData: any,
  previousData?: any
) => {
  const event = previousData ? 'quote.updated' : 'quote.created'
  const payload = createWebhookPayload(event, quoteData, previousData)
  
  // Execute each action in the rule
  for (const action of rule.actions) {
    if (action.type === 'webhook') {
      const result = await sendWebhook(
        action.config.url,
        payload,
        action.config.headers || {}
      )
      
      await logWebhookExecution(
        rule.id,
        quoteData.id,
        'webhook',
        action.config.url,
        payload,
        result
      )
    }
  }
}

// GHL-specific webhook payload format
export const createGHLPayload = (quoteData: any) => {
  return {
    // Standard contact fields
    firstName: quoteData.name.split(' ')[0],
    lastName: quoteData.name.split(' ').slice(1).join(' ') || '',
    email: quoteData.email,
    phone: quoteData.phone || '',
    companyName: quoteData.company || '',
    
    // Custom fields for GHL
    customFields: {
      project_type: quoteData.project_type,
      industry: quoteData.industry,
      budget: quoteData.budget,
      timeline: quoteData.timeline,
      key_features: quoteData.key_features?.join(', ') || '',
      integrations: quoteData.integrations?.join(', ') || '',
      wants_blog_account: quoteData.wants_account ? 'Yes' : 'No',
      quote_status: quoteData.status,
      lead_source: 'Website Quote Form',
      quote_id: quoteData.id
    },
    
    // Tags for GHL automation
    tags: [
      'website-lead',
      `budget-${quoteData.budget.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`,
      `industry-${quoteData.industry.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`,
      `project-${quoteData.project_type.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`
    ],
    
    // Opportunity data
    opportunity: {
      name: `${quoteData.project_type} - ${quoteData.company || quoteData.name}`,
      stage: 'quote-requested',
      value: getBudgetValue(quoteData.budget),
      source: 'Website Quote Form'
    }
  }
}

// Convert budget string to numeric value for GHL
const getBudgetValue = (budget: string): number => {
  if (budget.includes('$100,000+')) return 100000
  if (budget.includes('$50,000')) return 75000
  if (budget.includes('$25,000')) return 37500
  if (budget.includes('$10,000')) return 17500
  return 10000
}

// Slack-specific webhook payload format
export const createSlackPayload = (quoteData: any) => {
  const budgetEmoji = getBudgetValue(quoteData.budget) >= 50000 ? '💰' : '💵'
  
  return {
    text: `${budgetEmoji} New ${quoteData.budget} quote from ${quoteData.name}`,
    blocks: [
      {
        type: 'header',
        text: {
          type: 'plain_text',
          text: `New Quote: ${quoteData.project_type}`
        }
      },
      {
        type: 'section',
        fields: [
          {
            type: 'mrkdwn',
            text: `*Name:*\n${quoteData.name}`
          },
          {
            type: 'mrkdwn', 
            text: `*Email:*\n${quoteData.email}`
          },
          {
            type: 'mrkdwn',
            text: `*Company:*\n${quoteData.company || 'Not provided'}`
          },
          {
            type: 'mrkdwn',
            text: `*Budget:*\n${quoteData.budget}`
          },
          {
            type: 'mrkdwn',
            text: `*Industry:*\n${quoteData.industry}`
          },
          {
            type: 'mrkdwn',
            text: `*Timeline:*\n${quoteData.timeline}`
          }
        ]
      },
      {
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Key Features:*\n${quoteData.key_features?.join(', ') || 'None specified'}`
        }
      },
      {
        type: 'actions',
        elements: [
          {
            type: 'button',
            text: {
              type: 'plain_text',
              text: 'View in Dashboard'
            },
            url: `${window.location.origin}/admin/quotes`,
            style: 'primary'
          },
          {
            type: 'button',
            text: {
              type: 'plain_text', 
              text: 'Send Email'
            },
            url: `mailto:${quoteData.email}?subject=Re: Your ${quoteData.project_type} Project`
          }
        ]
      }
    ]
  }
}
