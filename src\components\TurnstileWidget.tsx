import React, { useEffect, useRef, useState } from 'react'
import { useTheme } from '@/components/ThemeProvider'

interface TurnstileWidgetProps {
  onVerify: (token: string) => void
  onError?: () => void
  onExpire?: () => void
  className?: string
  size?: 'normal' | 'compact'
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement, options: any) => string
      reset: (widgetId: string) => void
      remove: (widgetId: string) => void
      getResponse: (widgetId: string) => string
    }
  }
}

const TurnstileWidget: React.FC<TurnstileWidgetProps> = ({
  onVerify,
  onError,
  onExpire,
  className = '',
  size = 'normal'
}) => {
  const { theme } = useTheme()
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetIdRef = useRef<string | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [isScriptLoaded, setIsScriptLoaded] = useState(false)

  const siteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY

  // Load Turnstile script
  useEffect(() => {
    if (!siteKey) {
      console.warn('Turnstile site key not configured')
      return
    }

    if (window.turnstile) {
      setIsScriptLoaded(true)
      return
    }

    const script = document.createElement('script')
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js'
    script.async = true
    script.defer = true
    
    script.onload = () => {
      setIsScriptLoaded(true)
    }
    
    script.onerror = () => {
      console.error('Failed to load Turnstile script')
      onError?.()
    }

    document.head.appendChild(script)

    return () => {
      if (document.head.contains(script)) {
        document.head.removeChild(script)
      }
    }
  }, [siteKey, onError])

  // Render widget when script is loaded
  useEffect(() => {
    if (!isScriptLoaded || !containerRef.current || !siteKey || isLoaded) {
      return
    }

    try {
      const widgetId = window.turnstile.render(containerRef.current, {
        sitekey: siteKey,
        theme: theme === 'dark' ? 'dark' : 'light',
        size,
        callback: (token: string) => {
          onVerify(token)
        },
        'error-callback': () => {
          onError?.()
        },
        'expired-callback': () => {
          onExpire?.()
        },
        'timeout-callback': () => {
          onError?.()
        }
      })

      widgetIdRef.current = widgetId
      setIsLoaded(true)
    } catch (error) {
      console.error('Failed to render Turnstile widget:', error)
      onError?.()
    }
  }, [isScriptLoaded, siteKey, theme, size, onVerify, onError, onExpire, isLoaded])

  // Update theme when it changes
  useEffect(() => {
    if (widgetIdRef.current && isLoaded) {
      // Reset and re-render with new theme
      window.turnstile.remove(widgetIdRef.current)
      setIsLoaded(false)
    }
  }, [theme])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (widgetIdRef.current) {
        try {
          window.turnstile.remove(widgetIdRef.current)
        } catch (error) {
          console.warn('Failed to cleanup Turnstile widget:', error)
        }
      }
    }
  }, [])

  // Public methods
  const reset = () => {
    if (widgetIdRef.current) {
      window.turnstile.reset(widgetIdRef.current)
    }
  }

  const getResponse = (): string => {
    if (widgetIdRef.current) {
      return window.turnstile.getResponse(widgetIdRef.current)
    }
    return ''
  }

  // Expose methods via ref
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    reset,
    getResponse
  }))

  if (!siteKey) {
    return (
      <div className={`p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg ${className}`}>
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          ⚠️ Captcha not configured. Please set VITE_TURNSTILE_SITE_KEY environment variable.
        </p>
      </div>
    )
  }

  return (
    <div className={`turnstile-container ${className}`}>
      <div ref={containerRef} />
      {!isScriptLoaded && (
        <div className="flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Loading security check...</span>
        </div>
      )}
    </div>
  )
}

export default TurnstileWidget
