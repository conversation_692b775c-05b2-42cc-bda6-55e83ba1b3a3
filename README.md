
# Millennial Business Innovations

## About

Millennial Business Innovations is a digital solutions company that helps startups and businesses transform their ideas into digital reality. We specialize in MVP development, custom web solutions, landing pages, and SaaS platforms.

## Project Overview

This is the official website for Millennial Business Innovations, built with modern web technologies to showcase our services and provide an engaging user experience for potential clients.

## Technologies Used

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript development
- **React** - Frontend library for building user interfaces
- **Tailwind CSS** - Utility-first CSS framework for styling
- **Shadcn/UI** - Modern UI component library
- **Lucide React** - Beautiful icon library
- **React Router** - Client-side routing
- **TanStack Query** - Data fetching and state management

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd millennial-business-innovations
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

To create a production build:

```bash
npm run build
```

The built files will be generated in the `dist` directory.

### Preview Production Build

To preview the production build locally:

```bash
npm run preview
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (shadcn/ui)
│   ├── HeroSection.tsx
│   ├── AboutSection.tsx
│   ├── ServicesSection.tsx
│   └── ...
├── pages/              # Page components
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
└── main.tsx           # Application entry point
```

## Features

- **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- **Modern UI** - Clean, professional design with smooth animations and glassmorphism effects
- **Dark/Light Theme** - Toggle between light and dark modes with system preference detection
- **Performance Optimized** - Fast loading times and efficient rendering
- **SEO Friendly** - Proper meta tags and semantic HTML structure
- **Calendar Integration** - GHL (GoHighLevel) calendar booking integration for consultations
- **Service Showcase** - Detailed presentation of our offerings with interactive cards
- **Portfolio Section** - Display of completed projects with external links
- **Legal Pages** - Privacy policy and terms & conditions pages
- **Glassmorphism Design** - Modern glass-like UI elements with backdrop blur effects
- **Cyan-Blue Brand Theme** - Consistent brand colors throughout the application

## Recent Updates

### Brand Color Consistency (Latest)
- Updated all purple color gradients to cyan-blue theme
- Fixed header "Book a Call" button colors (desktop and mobile)
- Updated About section title and background elements
- Fixed Services section card hover effects
- Maintained consistent brand identity throughout the application

### Theme Implementation
- Added comprehensive dark/light theme support
- Implemented glassmorphism design elements
- Updated all components for theme compatibility
- Added theme toggle with system preference detection

### Calendar Integration
- Integrated GHL (GoHighLevel) calendar booking system
- Added consistent popup behavior for all "Book a Call" buttons
- Implemented calendar modal component

### Portfolio Enhancement
- Added external project links
- Created dedicated portfolio page
- Implemented responsive project showcase

## Contact

For inquiries about our services or this website, please contact us at:

- **Email**: <EMAIL>
- **Website**: [millennialbusinessinnovations.com](https://millennialbusinessinnovations.com)

## License

© 2024 Millennial Business Innovations. All rights reserved.
