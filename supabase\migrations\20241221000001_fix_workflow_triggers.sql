-- Fix Workflow Triggers - Connect Database Triggers to Actual Workflow Execution
-- This migration ensures that when quotes are created or newsletters are subscribed,
-- the workflows actually execute instead of just logging

-- Drop existing triggers to avoid conflicts
DROP TRIGGER IF EXISTS trigger_workflows_on_quote_created ON quote_requests;
DROP TRIGGER IF EXISTS execute_workflows_on_quote_created ON quote_requests;
DROP TRIGGER IF EXISTS quote_automation_trigger ON quote_requests;
DROP TRIGGER IF EXISTS newsletter_workflow_trigger ON newsletter_subscribers;
DROP TRIGGER IF EXISTS execute_workflows_on_newsletter_signup ON newsletter_subscribers;
DROP TRIGGER IF EXISTS execute_workflows_on_blog_created ON blog_posts;
DROP TRIGGER IF EXISTS execute_workflows_on_blog_comment ON blog_comments;
DROP TRIGGER IF EXISTS execute_workflows_on_blog_reaction ON blog_reactions;

-- Drop existing functions
DROP FUNCTION IF EXISTS trigger_workflows_on_quote();
DROP FUNCTION IF EXISTS trigger_quote_automation();
DROP FUNCTION IF EXISTS trigger_newsletter_workflows();

-- Create unified workflow trigger function
CREATE OR REPLACE FUNCTION execute_workflow_triggers()
RETURNS TRIGGER AS $$
DECLARE
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id UUID;
    trigger_type_value TEXT;
    function_url TEXT;
    response_status INTEGER;
BEGIN
    -- Determine trigger type based on table
    IF TG_TABLE_NAME = 'quote_requests' THEN
        trigger_type_value := 'new_quote';
        trigger_data := jsonb_build_object(
            'quote', row_to_json(NEW),
            'contact', jsonb_build_object(
                'id', NEW.id,
                'name', NEW.name,
                'email', NEW.email,
                'company', NEW.company,
                'phone', NEW.phone
            ),
            'event', 'quote.created',
            'timestamp', NOW()
        );
    ELSIF TG_TABLE_NAME = 'newsletter_subscribers' THEN
        -- Only trigger on new subscriptions or reactivations
        IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active') THEN
            trigger_type_value := 'newsletter_signup';
            trigger_data := jsonb_build_object(
                'email', NEW.email,
                'name', NEW.name,
                'source', NEW.source,
                'subscriber_id', NEW.id,
                'subscribed_at', NEW.subscribed_at,
                'metadata', NEW.metadata,
                'event', 'newsletter.subscribed',
                'timestamp', NOW()
            );
        ELSE
            -- Don't trigger workflows for other newsletter updates
            RETURN NEW;
        END IF;
    ELSIF TG_TABLE_NAME = 'blog_posts' THEN
        -- Only trigger on new blog posts
        IF TG_OP = 'INSERT' THEN
            trigger_type_value := 'blog_created';
            trigger_data := jsonb_build_object(
                'blog_post', row_to_json(NEW),
                'post_id', NEW.id,
                'title', NEW.title,
                'author_id', NEW.author_id,
                'status', NEW.status,
                'category', NEW.category,
                'event', 'blog.created',
                'timestamp', NOW()
            );
        ELSE
            -- Don't trigger workflows for blog updates
            RETURN NEW;
        END IF;
    ELSIF TG_TABLE_NAME = 'blog_comments' THEN
        trigger_type_value := 'blog_comment';
        trigger_data := jsonb_build_object(
            'comment', row_to_json(NEW),
            'comment_id', NEW.id,
            'post_id', NEW.post_id,
            'user_id', NEW.user_id,
            'content', NEW.content,
            'guest_email', NEW.guest_email,
            'guest_name', NEW.guest_name,
            'is_anonymous', NEW.is_anonymous,
            'parent_id', NEW.parent_id,
            'event', 'blog.comment_created',
            'timestamp', NOW()
        );
    ELSIF TG_TABLE_NAME = 'blog_reactions' THEN
        trigger_type_value := 'blog_reaction';
        trigger_data := jsonb_build_object(
            'reaction', row_to_json(NEW),
            'reaction_id', NEW.id,
            'post_id', NEW.post_id,
            'user_id', NEW.user_id,
            'anonymous_id', NEW.anonymous_id,
            'reaction_type', NEW.reaction_type,
            'event', 'blog.reaction_created',
            'timestamp', NOW()
        );
    ELSE
        -- Unknown table, skip
        RETURN NEW;
    END IF;

    -- Find active workflows with matching trigger type
    FOR workflow_record IN
        SELECT w.id, w.name, w.nodes
        FROM workflows w
        WHERE w.active = true
        AND w.is_template = false
        AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(w.nodes) AS node
            WHERE node->>'type' = 'trigger'
            AND node->'data'->>'triggerType' = trigger_type_value
        )
    LOOP
        -- Log workflow execution as pending
        INSERT INTO workflow_executions (
            workflow_id,
            workflow_name,
            trigger_data,
            status,
            executed_at
        ) VALUES (
            workflow_record.id,
            workflow_record.name,
            trigger_data,
            'pending',
            NOW()
        ) RETURNING id INTO execution_id;

        -- Call the execute-workflow Edge Function asynchronously
        -- Using pg_net extension if available, otherwise log for manual processing
        BEGIN
            -- Try to call the Edge Function
            SELECT net.http_post(
                url := current_setting('app.supabase_url') || '/functions/v1/execute-workflow',
                headers := jsonb_build_object(
                    'Content-Type', 'application/json',
                    'Authorization', 'Bearer ' || current_setting('app.supabase_service_role_key')
                ),
                body := jsonb_build_object(
                    'workflow_id', workflow_record.id,
                    'execution_id', execution_id,
                    'trigger_data', trigger_data
                )
            ) INTO response_status;

            -- Log successful trigger
            RAISE NOTICE 'Triggered workflow % (execution: %)', workflow_record.name, execution_id;

        EXCEPTION WHEN OTHERS THEN
            -- If Edge Function call fails, update execution status
            UPDATE workflow_executions 
            SET 
                status = 'failed',
                error_message = 'Failed to call execution function: ' || SQLERRM
            WHERE id = execution_id;
            
            RAISE NOTICE 'Failed to trigger workflow %: %', workflow_record.name, SQLERRM;
        END;
    END LOOP;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for quote requests
CREATE TRIGGER execute_workflows_on_quote_created
    AFTER INSERT ON quote_requests
    FOR EACH ROW
    EXECUTE FUNCTION execute_workflow_triggers();

-- Create triggers for newsletter subscriptions
CREATE TRIGGER execute_workflows_on_newsletter_signup
    AFTER INSERT OR UPDATE ON newsletter_subscribers
    FOR EACH ROW
    EXECUTE FUNCTION execute_workflow_triggers();

-- Create triggers for blog posts
CREATE TRIGGER execute_workflows_on_blog_created
    AFTER INSERT ON blog_posts
    FOR EACH ROW
    EXECUTE FUNCTION execute_workflow_triggers();

-- Create triggers for blog comments
CREATE TRIGGER execute_workflows_on_blog_comment
    AFTER INSERT ON blog_comments
    FOR EACH ROW
    EXECUTE FUNCTION execute_workflow_triggers();

-- Create triggers for blog reactions
CREATE TRIGGER execute_workflows_on_blog_reaction
    AFTER INSERT ON blog_reactions
    FOR EACH ROW
    EXECUTE FUNCTION execute_workflow_triggers();

-- Create function to manually trigger workflows (for testing and debugging)
CREATE OR REPLACE FUNCTION manual_trigger_workflow(
    trigger_type_param TEXT,
    data_param JSONB DEFAULT '{}'::JSONB
)
RETURNS TABLE(
    workflow_id UUID,
    workflow_name TEXT,
    execution_id UUID,
    status TEXT
) AS $$
DECLARE
    workflow_record RECORD;
    trigger_data JSONB;
    execution_id_var UUID;
BEGIN
    -- Build trigger data
    trigger_data := jsonb_build_object(
        'manual_trigger', true,
        'trigger_type', trigger_type_param,
        'data', data_param,
        'timestamp', NOW()
    );

    -- Find matching workflows
    FOR workflow_record IN
        SELECT w.id, w.name, w.nodes
        FROM workflows w
        WHERE w.active = true
        AND w.is_template = false
        AND EXISTS (
            SELECT 1
            FROM jsonb_array_elements(w.nodes) AS node
            WHERE node->>'type' = 'trigger'
            AND node->'data'->>'triggerType' = trigger_type_param
        )
    LOOP
        -- Log execution
        INSERT INTO workflow_executions (
            workflow_id,
            workflow_name,
            trigger_data,
            status,
            executed_at
        ) VALUES (
            workflow_record.id,
            workflow_record.name,
            trigger_data,
            'pending',
            NOW()
        ) RETURNING id INTO execution_id_var;

        -- Return result
        workflow_id := workflow_record.id;
        workflow_name := workflow_record.name;
        execution_id := execution_id_var;
        status := 'triggered';
        
        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create function to check workflow trigger status
CREATE OR REPLACE FUNCTION check_workflow_triggers()
RETURNS TABLE(
    table_name TEXT,
    trigger_name TEXT,
    function_name TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.event_object_table::TEXT,
        t.trigger_name::TEXT,
        t.action_statement::TEXT,
        CASE 
            WHEN t.trigger_name IS NOT NULL THEN 'active'
            ELSE 'missing'
        END::TEXT
    FROM information_schema.triggers t
    WHERE t.trigger_schema = 'public'
    AND t.trigger_name IN (
        'execute_workflows_on_quote_created',
        'execute_workflows_on_newsletter_signup',
        'execute_workflows_on_blog_created',
        'execute_workflows_on_blog_comment',
        'execute_workflows_on_blog_reaction'
    )
    ORDER BY t.event_object_table, t.trigger_name;
END;
$$ LANGUAGE plpgsql;

-- Set up configuration for Edge Function calls (these need to be set by admin)
-- You'll need to run these commands in your Supabase dashboard:
-- SELECT set_config('app.supabase_url', 'https://your-project.supabase.co', false);
-- SELECT set_config('app.supabase_service_role_key', 'your-service-role-key', false);

-- Create a helper function to set these configs
CREATE OR REPLACE FUNCTION setup_workflow_config(
    supabase_url_param TEXT,
    service_role_key_param TEXT
)
RETURNS TEXT AS $$
BEGIN
    PERFORM set_config('app.supabase_url', supabase_url_param, false);
    PERFORM set_config('app.supabase_service_role_key', service_role_key_param, false);
    
    RETURN 'Configuration updated successfully';
END;
$$ LANGUAGE plpgsql;

-- Test the trigger setup
SELECT check_workflow_triggers();
