import { Node, mergeAttributes } from '@tiptap/core'

export interface CustomHtmlOptions {
  HTMLAttributes: Record<string, any>
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    customHtml: {
      setCustomHtml: (options: { content: string }) => ReturnType
    }
  }
}

export const CustomHtml = Node.create<CustomHtmlOptions>({
  name: 'customHtml',

  group: 'block',

  atom: true,

  addOptions() {
    return {
      HTMLAttributes: {
        class: 'custom-html-block',
      },
    }
  },

  addAttributes() {
    return {
      content: {
        default: '',
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-type="custom-html"]',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(this.options.HTMLAttributes, {
        'data-type': 'custom-html',
        'data-content': HTMLAttributes.content || '',
        style: 'border: 2px dashed #ccc; padding: 16px; margin: 16px 0; border-radius: 8px; background: #f9f9f9;',
      }),
      [
        'div',
        {
          innerHTML: HTMLAttributes.content || '<p>Custom HTML Block - Click to edit</p>',
        },
      ],
    ]
  },

  addCommands() {
    return {
      setCustomHtml:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})

export default CustomHtml
