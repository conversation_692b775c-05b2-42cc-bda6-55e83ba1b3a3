if(!self.define){let e,i={};const s=(s,r)=>(s=new URL(s+".js",r).href,i[s]||new Promise((i=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=i,document.head.appendChild(e)}else e=s,importScripts(s),i()})).then((()=>{let e=i[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e})));self.define=(r,n)=>{const o=e||("document"in self?document.currentScript.src:"")||location.href;if(i[o])return;let f={};const d=e=>s(e,o),l={module:{uri:o},exports:f,require:d};i[o]=Promise.all(r.map((e=>l[e]||d(e)))).then((e=>(n(...e),f)))}}define(["./workbox-5ffe50d4"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"api/webhooks/quote/created.js",revision:"e9263f2d64dbe294f31dfcd77dd6b111"},{url:"assets/emailService-B6DCljgz.js",revision:null},{url:"assets/index-2_5tWdjc.js",revision:null},{url:"assets/index-B6-IAiUQ.css",revision:null},{url:"assets/security-DVn_p-Jj.js",revision:null},{url:"assets/workbox-window.prod.es5-B9K5rw8f.js",revision:null},{url:"favicon.ico",revision:"566e64364d6957715dc11845f4800700"},{url:"favicon.svg",revision:"9f198425f04935639eed34ca00a3fd56"},{url:"index.html",revision:"f1fa9a11167f2a9f4bfb1c9b46e49db2"},{url:"og-image.png",revision:"2bd42e030f6d90f51f5c34de7cb3f4f7"},{url:"placeholder.svg",revision:"35707bd9960ba5281c72af927b79291f"},{url:"pwa-192x192.png",revision:"566e64364d6957715dc11845f4800700"},{url:"pwa-192x192.svg",revision:"07598f80a332e5d8a080eab023eef57c"},{url:"pwa-512x512.png",revision:"566e64364d6957715dc11845f4800700"},{url:"pwa-512x512.svg",revision:"33b097b12163ca9a6a30c05266f78c32"},{url:"favicon.ico",revision:"566e64364d6957715dc11845f4800700"},{url:"favicon.svg",revision:"9f198425f04935639eed34ca00a3fd56"},{url:"pwa-192x192.png",revision:"566e64364d6957715dc11845f4800700"},{url:"pwa-512x512.png",revision:"566e64364d6957715dc11845f4800700"},{url:"manifest.webmanifest",revision:"57f460ff096b668ba41b6af5afdadf6a"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html")))}));
