import DOMPurify from 'dompurify'

// File upload security configuration
export const ALLOWED_FILE_TYPES = {
  images: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
  documents: ['application/pdf', 'text/plain'],
  videos: ['video/mp4', 'video/webm', 'video/ogg']
} as const

export const MAX_FILE_SIZES = {
  image: 5 * 1024 * 1024, // 5MB
  document: 10 * 1024 * 1024, // 10MB
  video: 50 * 1024 * 1024 // 50MB
} as const

// Dangerous file extensions that should never be allowed
const DANGEROUS_EXTENSIONS = [
  'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'php', 'asp', 'aspx', 'jsp'
]

/**
 * Validates file security before upload
 */
export const validateFileUpload = (file: File): { isValid: boolean; error?: string } => {
  // Check file size
  const fileType = getFileCategory(file.type)
  const maxSize = MAX_FILE_SIZES[fileType]
  
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(maxSize / 1024 / 1024)}MB limit`
    }
  }

  // Check file type
  const allowedTypes = Object.values(ALLOWED_FILE_TYPES).flat()
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'File type not allowed'
    }
  }

  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase()
  if (!extension || DANGEROUS_EXTENSIONS.includes(extension)) {
    return {
      isValid: false,
      error: 'File extension not allowed'
    }
  }

  // Check for double extensions (e.g., file.jpg.exe)
  const nameParts = file.name.split('.')
  if (nameParts.length > 2) {
    const secondToLastExt = nameParts[nameParts.length - 2]?.toLowerCase()
    if (DANGEROUS_EXTENSIONS.includes(secondToLastExt)) {
      return {
        isValid: false,
        error: 'Suspicious file name detected'
      }
    }
  }

  return { isValid: true }
}

/**
 * Gets file category for size validation
 */
const getFileCategory = (mimeType: string): keyof typeof MAX_FILE_SIZES => {
  if (mimeType.startsWith('image/')) return 'image'
  if (mimeType.startsWith('video/')) return 'video'
  return 'document'
}

/**
 * Generates secure filename
 */
export const generateSecureFilename = (originalName: string): string => {
  const extension = originalName.split('.').pop()?.toLowerCase()
  const timestamp = Date.now()
  const randomString = Math.random().toString(36).substring(2, 15)
  
  return `${timestamp}_${randomString}.${extension}`
}

/**
 * Sanitizes HTML content to prevent XSS
 */
export const sanitizeHtml = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 's', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'code', 'pre'
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class'],
    ALLOW_DATA_ATTR: false,
    FORBID_SCRIPT: true,
    FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'iframe'],
    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'style']
  })
}

/**
 * Rate limiting for API calls
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  
  isAllowed(identifier: string, maxRequests: number = 10, windowMs: number = 60000): boolean {
    const now = Date.now()
    const windowStart = now - windowMs
    
    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, [])
    }
    
    const userRequests = this.requests.get(identifier)!
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(time => time > windowStart)
    
    if (validRequests.length >= maxRequests) {
      return false
    }
    
    validRequests.push(now)
    this.requests.set(identifier, validRequests)
    
    return true
  }
}

export const rateLimiter = new RateLimiter()

/**
 * Input validation and sanitization
 */
export const validateInput = {
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email) && email.length <= 254
  },
  
  name: (name: string): boolean => {
    return name.length >= 1 && name.length <= 100 && !/[<>]/.test(name)
  },
  
  password: (password: string): boolean => {
    return password.length >= 8 && password.length <= 128
  },
  
  url: (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      return ['http:', 'https:'].includes(urlObj.protocol)
    } catch {
      return false
    }
  }
}

/**
 * Content Security Policy headers
 */
export const CSP_HEADER = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://calendar.aha-innovations.com https://challenges.cloudflare.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https: blob:;
  media-src 'self' https:;
  connect-src 'self' https://kwilluhxhthdrqomkecn.supabase.co wss://kwilluhxhthdrqomkecn.supabase.co;
  frame-src 'self' https://calendar.aha-innovations.com https://challenges.cloudflare.com;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
  upgrade-insecure-requests;
`.replace(/\s+/g, ' ').trim()
