import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import BlogPromotionToast from "@/components/BlogPromotionToast";
import PWAInstallPrompt from "@/components/PWAInstallPrompt";
import AIAssistant from "@/components/AIAssistant";
import Index from "./pages/Index";
import Portfolio from "./pages/Portfolio";
import Testimonials from "./pages/Testimonials";
import CaseStudies from "./pages/CaseStudies";
import Blog from "./pages/Blog";
import BlogPost from "./pages/BlogPost";
import BlogDashboard from "./pages/admin/BlogDashboard";
import BlogEditor from "./pages/admin/BlogEditor";
import CreateTestimonial from "./pages/admin/CreateTestimonial";
import ProfileSettings from "./pages/admin/ProfileSettings";
import UserManagement from "./pages/admin/UserManagement";
import Changelog from "./pages/admin/Changelog";
import SystemSettings from "./pages/admin/SystemSettings";
import QuoteRequests from "./pages/admin/QuoteRequests";
import Automations from "./pages/admin/Automations";
import WorkflowEditor from "./pages/admin/WorkflowEditor";
import EmailIntegrations from "./pages/admin/EmailIntegrations";
import CreditsUsage from "./pages/admin/CreditsUsage";
import Pricing from "./pages/Pricing";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsAndConditions from "./pages/TermsAndConditions";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light" storageKey="mbi-ui-theme">
      <AuthProvider>
        <OrganizationProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/portfolio" element={<Portfolio />} />
              <Route path="/testimonials" element={<Testimonials />} />
              <Route path="/case-studies" element={<CaseStudies />} />
              <Route path="/blog" element={<Blog />} />
              <Route path="/blog/:slug" element={<BlogPost />} />
              <Route path="/admin/blog" element={<BlogDashboard />} />
              <Route path="/admin/blog/new" element={<BlogEditor />} />
              <Route path="/admin/blog/edit/:id" element={<BlogEditor />} />
              <Route path="/admin/blog/testimonial" element={<CreateTestimonial />} />
              <Route path="/admin/blog/profile" element={<ProfileSettings />} />
              <Route path="/admin/blog/users" element={<UserManagement />} />
              <Route path="/admin/blog/changelog" element={<Changelog />} />
              <Route path="/admin/quotes" element={<QuoteRequests />} />
              <Route path="/admin/automations" element={<Automations />} />
              <Route path="/admin/automations/editor/:id" element={<WorkflowEditor />} />
              <Route path="/admin/email-integrations" element={<EmailIntegrations />} />
              <Route path="/admin/credits" element={<CreditsUsage />} />
              <Route path="/admin/system-settings" element={<SystemSettings />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/terms-and-conditions" element={<TermsAndConditions />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
            {/* Blog Promotion Toast - appears on non-blog pages for non-authenticated users */}
            <BlogPromotionToast />
            {/* PWA Install Prompt */}
            <PWAInstallPrompt />
            {/* AI Assistant - appears on admin pages for authenticated users */}
            <AIAssistant />
          </BrowserRouter>
          </TooltipProvider>
        </OrganizationProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
