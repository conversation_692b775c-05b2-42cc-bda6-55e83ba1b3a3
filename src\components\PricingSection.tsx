
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import CalendarModal from './CalendarModal';
import QuoteRequestModal from './QuoteRequestModal';

const PricingSection = () => {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);

  const plans = [
    {
      name: 'Starter Package',
      price: '$12,000',
      period: 'starting price',
      description: 'Perfect for startups and small businesses looking to launch their MVP.',
      features: [
        'MVP Development',
        'Landing Page Design',
        'Initial Strategy Session',
        'Basic Support (60 days)',
        'Responsive Design',
        'SEO Foundation',
        'Performance Optimization',
        'Security Best Practices'
      ],
      highlighted: false,
      cta: 'Get Started'
    },
    {
      name: 'Professional Package',
      price: '$35,000',
      period: 'starting price',
      description: 'Ideal for growing businesses that need comprehensive digital solutions.',
      features: [
        'Everything in Starter',
        'Custom Web Applications',
        'Advanced Database Design',
        'Extended Support (120 days)',
        'Advanced Analytics Integration',
        'Third-party API Integration',
        'Cloud Infrastructure Setup',
        'Advanced Security Implementation',
        'Performance Monitoring'
      ],
      highlighted: true,
      cta: 'Most Popular'
    },
    {
      name: 'Enterprise Package',
      price: '$75,000+',
      period: 'custom pricing',
      description: 'Comprehensive SaaS platforms and enterprise solutions for serious businesses.',
      features: [
        'Everything in Professional',
        'Full SaaS Platform Development',
        'Digital Strategy Consulting ($250/hr)',
        'Dedicated Development Team',
        'Long-term Partnership',
        'Dedicated Project Manager',
        '24/7 Priority Support',
        'Enterprise Security Features',
        'Scalability Architecture',
        'Custom Integrations & APIs',
        'DevOps & CI/CD Pipeline',
        'Ongoing Maintenance Plans'
      ],
      highlighted: false,
      cta: 'Schedule Consultation'
    }
  ];

  return (
    <section id="pricing" className="py-12 sm:py-16 md:py-20 bg-gradient-to-b from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-blue-900/10 dark:to-cyan-900/10 relative overflow-hidden">
      {/* Glassmorphism background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-10 right-20 w-48 h-48 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 left-20 w-64 h-64 bg-gradient-to-r from-cyan-300/15 to-teal-300/15 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 left-1/4 w-36 h-36 bg-gradient-to-r from-teal-300/10 to-blue-300/10 rounded-full blur-lg"></div>
      </div>
      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl md:text-5xl text-gray-900 dark:text-white mb-4 sm:mb-6">
            Enterprise-Grade <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Solutions</span>
          </h2>
          <p className="font-poppins text-base sm:text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-4xl mx-auto px-4 sm:px-0">
            Transform your startup idea into a scalable, secure, and profitable digital platform. Our pricing reflects the enterprise-level quality and strategic value we deliver.
          </p>
          <div className="mt-6 flex flex-wrap justify-center gap-4 text-sm text-gray-500 dark:text-gray-400">
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              No hidden fees
            </span>
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              Fixed-price projects
            </span>
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              Money-back guarantee
            </span>
          </div>
        </div>
        
        <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-6xl mx-auto px-4 sm:px-0">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative overflow-hidden transition-all duration-300 hover:transform hover:scale-105 flex flex-col h-full ${
                plan.highlighted
                  ? 'bg-gradient-to-b from-white to-gray-50 dark:from-gray-800/90 dark:to-gray-900/90 border-2 border-primary scale-105 shadow-xl shadow-primary/20'
                  : 'bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10'
              } backdrop-blur-lg`}
            >
              {plan.highlighted && (
                <div className="absolute top-0 left-0 right-0 bg-gradient-to-r from-primary to-accent text-white text-center py-2 text-sm font-poppins font-semibold">
                  MOST POPULAR
                </div>
              )}

              <CardHeader className={`text-center ${plan.highlighted ? 'pt-12' : 'pt-8'} px-4 sm:px-6`}>
                <CardTitle className="font-montserrat text-xl sm:text-2xl text-gray-900 dark:text-white mb-2">
                  {plan.name}
                </CardTitle>
                <div className="mb-4">
                  <span className="font-montserrat font-bold text-3xl sm:text-4xl text-gray-900 dark:text-white">
                    {plan.price}
                  </span>
                  <span className={`font-poppins ml-2 text-sm ${plan.highlighted ? 'text-gray-600 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}`}>
                    {plan.period}
                  </span>
                </div>
                <p className={`font-poppins text-sm ${plan.highlighted ? 'text-gray-700 dark:text-gray-200' : 'text-gray-600 dark:text-gray-300'}`}>
                  {plan.description}
                </p>
              </CardHeader>
              
              <CardContent className="space-y-6 px-4 sm:px-6 flex-1 flex flex-col">
                <ul className="space-y-3 flex-1">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className={`flex items-center font-poppins text-sm ${plan.highlighted ? 'text-gray-700 dark:text-gray-200' : 'text-gray-600 dark:text-gray-300'}`}>
                      <span className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full py-3 font-poppins font-semibold transition-all duration-300 mt-auto ${
                    plan.highlighted
                      ? 'bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white shadow-lg'
                      : 'bg-gray-100 dark:bg-white/10 hover:bg-gray-200 dark:hover:bg-white/20 text-gray-900 dark:text-white border border-gray-300 dark:border-white/20'
                  }`}
                >
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-2xl p-8 mb-8">
            <h3 className="font-montserrat font-bold text-2xl text-gray-900 dark:text-white mb-4">
              Ready to Build Something Amazing?
            </h3>
            <p className="font-poppins text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Join 50+ successful startups who've transformed their ideas into profitable digital platforms with our expert team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-8 py-3 text-lg"
                onClick={() => setIsCalendarOpen(true)}
              >
                Get Your Free Strategy Session
              </Button>
              <Button
                variant="outline"
                className="border-primary text-primary hover:bg-primary/10 font-poppins px-8 py-3 text-lg"
                onClick={() => setIsQuoteModalOpen(true)}
              >
                Request Custom Quote
              </Button>
            </div>
          </div>

          <p className="font-poppins text-sm text-gray-500 dark:text-gray-400">
            💡 <strong>Limited Availability:</strong> We only take on 3-4 new projects per quarter to ensure exceptional quality.
          </p>
        </div>
      </div>

      <CalendarModal
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
      />

      <QuoteRequestModal
        isOpen={isQuoteModalOpen}
        onClose={() => setIsQuoteModalOpen(false)}
      />
    </section>
  );
};

export default PricingSection;
