var c=Object.defineProperty;var u=(o,r,e)=>r in o?c(o,r,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[r]=e;var a=(o,r,e)=>u(o,typeof r!="symbol"?r+"":r,e);class d{constructor(r){a(this,"integration");this.integration=r}async sendEmail(r){try{switch(this.integration.provider){case"gmail":return await this.sendGmailEmail(r);case"outlook":return await this.sendOutlookEmail(r);case"resend":return await this.sendResendEmail(r);case"postmark":return await this.sendPostmarkEmail(r);case"sendgrid":return await this.sendSendGridEmail(r);case"mailgun":return await this.sendMailgunEmail(r);case"smtp":return await this.sendSMTPEmail(r);default:return{success:!1,error:`Unsupported email provider: ${this.integration.provider}`}}}catch(e){return{success:!1,error:e instanceof Error?e.message:"Unknown error occurred"}}}async testIntegration(r){const e={to:r,subject:"Test Email from MBI Workflow System",body:`
        <h2>Email Integration Test</h2>
        <p>This is a test email from your MBI workflow system.</p>
        <p><strong>Integration:</strong> ${this.integration.name}</p>
        <p><strong>Provider:</strong> ${this.integration.provider}</p>
        <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        <p>If you received this email, your integration is working correctly!</p>
      `};return await this.sendEmail(e)}async sendGmailEmail(r){return await this.sendServerSideEmail(r)}async sendOutlookEmail(r){return await this.sendServerSideEmail(r)}async sendResendEmail(r){return await this.sendServerSideEmail(r)}async sendPostmarkEmail(r){const e=this.integration.config;try{const t=await fetch("https://api.postmarkapp.com/email",{method:"POST",headers:{"X-Postmark-Server-Token":e.serverToken,"Content-Type":"application/json"},body:JSON.stringify({From:e.fromEmail,To:r.to,Subject:r.subject,HtmlBody:r.body})});return t.ok?{success:!0,messageId:(await t.json()).MessageID}:{success:!1,error:`Postmark API error: ${await t.text()}`}}catch(t){return{success:!1,error:`Postmark error: ${t instanceof Error?t.message:"Unknown error"}`}}}async sendSendGridEmail(r){const e=this.integration.config;try{const t=await fetch("https://api.sendgrid.com/v3/mail/send",{method:"POST",headers:{Authorization:`Bearer ${e.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({personalizations:[{to:[{email:r.to}]}],from:{email:e.fromEmail},subject:r.subject,content:[{type:"text/html",value:r.body}]})});return t.ok?{success:!0,messageId:`sendgrid_${Date.now()}`}:{success:!1,error:`SendGrid API error: ${await t.text()}`}}catch(t){return{success:!1,error:`SendGrid error: ${t instanceof Error?t.message:"Unknown error"}`}}}async sendMailgunEmail(r){const e=this.integration.config;try{const t=new FormData;t.append("from",e.fromEmail),t.append("to",r.to),t.append("subject",r.subject),t.append("html",r.body);const s=await fetch(`https://api.mailgun.net/v3/${e.domain}/messages`,{method:"POST",headers:{Authorization:`Basic ${btoa(`api:${e.apiKey}`)}`},body:t});return s.ok?{success:!0,messageId:(await s.json()).id}:{success:!1,error:`Mailgun API error: ${await s.text()}`}}catch(t){return{success:!1,error:`Mailgun error: ${t instanceof Error?t.message:"Unknown error"}`}}}async sendSMTPEmail(r){return await this.sendServerSideEmail(r)}async sendServerSideEmail(r){try{const t="https://kwilluhxhthdrqomkecn.supabase.co/functions/v1/send-email";console.log("🚀 Sending email via server-side function:",{functionUrl:t,integrationId:this.integration.id,to:r.to,subject:r.subject,provider:this.integration.provider});const s=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.89VHFRkJS7fIGMIX7m4MFQ2P5e1Vrwax4L3auo6HYsk"},body:JSON.stringify({integrationId:this.integration.id,to:r.to,subject:r.subject,body:r.body,testEmail:!0})});if(console.log("📡 Function response status:",s.status),!s.ok){const i=await s.json();return console.error("❌ Function error:",i),{success:!1,error:i.message||i.error||`Server error: ${s.status}`}}const n=await s.json();return console.log("✅ Function success:",n),{success:n.success,messageId:n.messageId,error:n.success?void 0:n.message}}catch(e){return console.error("🔥 Network error:",e),{success:!1,error:`Network error: ${e instanceof Error?e.message:"Unknown error"}`}}}}const m=o=>new d(o);export{d as EmailService,m as createEmailService};
