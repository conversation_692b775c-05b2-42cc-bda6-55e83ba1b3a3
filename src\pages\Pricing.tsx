import React, { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { 
  Check, 
  Zap, 
  Users, 
  Mail, 
  Sparkles, 
  Crown,
  ArrowRight,
  Star,
  Shield,
  Rocket
} from 'lucide-react'
import { SUBSCRIPTION_FEATURES } from '@/types/organization'
import { toast } from 'sonner'

const Pricing = () => {
  const { user, profile, loading } = useAuth()
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [highlightPlan, setHighlightPlan] = useState<string | null>(null)
  
  // Get highlight and source from URL params
  const highlight = searchParams.get('highlight')
  const fromTrigger = searchParams.get('from')

  useEffect(() => {
    // Redirect unauthenticated users to signup
    if (!loading && !user) {
      navigate('/auth?redirect=/pricing')
      return
    }

    // Set highlight based on URL params
    if (highlight) {
      setHighlightPlan('basic') // Most upsells lead to basic plan
    }
  }, [user, loading, navigate, highlight])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Rocket className="h-8 w-8 animate-pulse mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400">Loading pricing...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect
  }

  const currentPlan = profile?.subscription_plan || 'free'

  const handleUpgrade = (planId: string) => {
    // Check if user is owner/super admin - they can upgrade
    if (profile?.role === 'owner' || profile?.role === 'super_admin') {
      // TODO: Integrate with Stripe or payment processor
      console.log(`Upgrading to ${planId}`)
      // For now, just show success message for admins
      toast.success(`Upgrade to ${planId} plan initiated! (Admin access - Integration pending)`)
    } else {
      // Regular users see coming soon message
      toast.info('Payment Gateway Coming Soon!', {
        description: 'We\'re working on integrating secure payment processing. Stay tuned!'
      })
    }
  }

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free': return <Sparkles className="h-5 w-5" />
      case 'basic': return <Zap className="h-5 w-5" />
      case 'pro': return <Crown className="h-5 w-5" />
      case 'enterprise': return <Shield className="h-5 w-5" />
      default: return <Star className="h-5 w-5" />
    }
  }

  const getHighlightMessage = () => {
    if (!highlight || !fromTrigger) return null

    const messages = {
      workflow_credits: "🚀 Unlock unlimited workflow automation!",
      ai_credits: "✨ Get advanced AI assistance for your content!",
      team_features: "👥 Start collaborating with your team!",
      email_features: "📧 Supercharge your email automation!"
    }

    return messages[highlight as keyof typeof messages]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <Navigation />
      
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          {getHighlightMessage() && (
            <div className="mb-4">
              <Badge variant="secondary" className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                {getHighlightMessage()}
              </Badge>
            </div>
          )}
          
          <h1 className="font-montserrat font-bold text-4xl md:text-5xl text-gray-900 dark:text-white mb-4">
            Choose Your Plan
          </h1>
          <p className="font-poppins text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Unlock powerful automation and AI features. Content creation stays free forever!
          </p>
          
          {/* Current Plan Indicator */}
          {currentPlan !== 'free' && (
            <div className="mt-4">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Current Plan: {SUBSCRIPTION_FEATURES[currentPlan as keyof typeof SUBSCRIPTION_FEATURES].name}
              </Badge>
            </div>
          )}
        </div>

        {/* Free Forever Banner */}
        <div className="mb-8 text-center">
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-center gap-2 mb-2">
                <Sparkles className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold text-green-800 dark:text-green-200">
                  Content Creation is Always Free!
                </h3>
              </div>
              <p className="text-green-700 dark:text-green-300 text-sm">
                Blog posts, stories, social features, and basic AI assistance - completely free, forever.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {Object.entries(SUBSCRIPTION_FEATURES).map(([planId, plan]) => {
            const isCurrentPlan = currentPlan === planId
            const isHighlighted = highlightPlan === planId
            const canUpgrade = currentPlan === 'free' || 
              (currentPlan === 'basic' && ['pro', 'enterprise'].includes(planId)) ||
              (currentPlan === 'pro' && planId === 'enterprise')

            return (
              <Card 
                key={planId}
                className={`relative ${
                  isHighlighted 
                    ? 'ring-2 ring-purple-500 shadow-xl scale-105' 
                    : isCurrentPlan 
                      ? 'ring-2 ring-green-500' 
                      : ''
                } transition-all duration-300 hover:shadow-lg`}
              >
                {isHighlighted && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white">
                      Recommended
                    </Badge>
                  </div>
                )}
                
                {isCurrentPlan && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-green-500 text-white">
                      Current Plan
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    {getPlanIcon(planId)}
                    <CardTitle className="text-xl">{plan.name}</CardTitle>
                  </div>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">
                    ${plan.price}
                    <span className="text-sm font-normal text-gray-500">/month</span>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Key Metrics */}
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Workflow Credits:</span>
                      <span className="font-semibold">
                        {plan.workflowCredits === -1 ? 'Unlimited' : plan.workflowCredits.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>AI Requests:</span>
                      <span className="font-semibold">
                        {plan.aiCredits === -1 ? 'Unlimited' : plan.aiCredits.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Team Members:</span>
                      <span className="font-semibold">
                        {plan.maxTeamMembers === -1 ? 'Unlimited' : plan.maxTeamMembers}
                      </span>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {plan.features.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-start gap-2 text-sm">
                        <Check className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                      </div>
                    ))}
                    {plan.features.length > 4 && (
                      <div className="text-xs text-gray-500">
                        +{plan.features.length - 4} more features
                      </div>
                    )}
                  </div>

                  {/* CTA Button */}
                  <div className="pt-4">
                    {isCurrentPlan ? (
                      <Button disabled className="w-full">
                        <Check className="h-4 w-4 mr-2" />
                        Current Plan
                      </Button>
                    ) : canUpgrade ? (
                      <Button
                        onClick={() => handleUpgrade(planId)}
                        className={`w-full ${
                          isHighlighted
                            ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
                            : ''
                        }`}
                      >
                        {(profile?.role === 'owner' || profile?.role === 'super_admin') ? (
                          <>
                            Upgrade Now
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </>
                        ) : (
                          <>
                            Coming Soon
                            <Sparkles className="h-4 w-4 ml-2" />
                          </>
                        )}
                      </Button>
                    ) : (
                      <Button variant="outline" disabled className="w-full">
                        Contact Sales
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
          <div className="space-y-6">
            <div>
              <h3 className="font-semibold mb-2">Is content creation really free forever?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Yes! Blog posts, stories, social features, and basic AI assistance will always be free. 
                We only charge for advanced automation and premium AI features.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Can I change plans anytime?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Absolutely! You can upgrade or downgrade your plan at any time. 
                Changes take effect immediately with prorated billing.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">What happens if I exceed my limits?</h3>
              <p className="text-gray-600 dark:text-gray-300">
                We'll notify you when you're approaching your limits and offer easy upgrade options. 
                Your content and data are always safe.
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default Pricing
