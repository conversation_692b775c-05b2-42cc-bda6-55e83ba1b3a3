Stack trace:
Frame         Function      Args
0007FFFFA100  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9000) msys-2.0.dll+0x1FE8E
0007FFFFA100  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA3D8) msys-2.0.dll+0x67F9
0007FFFFA100  000210046832 (000210286019, 0007FFFF9FB8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA100  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA100  000210068E24 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA3E0  00021006A225 (0007FFFFA110, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC95600000 ntdll.dll
7FFC93420000 KERNEL32.DLL
7FFC92790000 KERNELBASE.dll
7FFC94940000 USER32.dll
7FFC92D60000 win32u.dll
7FFC933F0000 GDI32.dll
7FFC92E30000 gdi32full.dll
7FFC92CB0000 msvcp_win.dll
7FFC92B60000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC93AC0000 advapi32.dll
7FFC93D80000 msvcrt.dll
7FFC93E30000 sechost.dll
7FFC944E0000 RPCRT4.dll
7FFC91EA0000 CRYPTBASE.DLL
7FFC92D90000 bcryptPrimitives.dll
7FFC944A0000 IMM32.DLL
7FFC74830000 windhawk.dll
