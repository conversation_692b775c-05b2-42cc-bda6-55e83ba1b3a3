import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Plus, 
  Trash2, 
  Zap, 
  Settings, 
  Clock, 
  Webhook,
  Mail,
  Code,
  TestTube,
  Save
} from 'lucide-react'
import { toast } from 'sonner'

interface AutomationBuilderProps {
  isOpen: boolean
  onClose: () => void
  editingRule?: any
}

interface Condition {
  field: string
  operator: string
  value: string
}

interface Action {
  type: 'webhook' | 'email' | 'status_update'
  name: string
  config: any
}

const AutomationBuilder: React.FC<AutomationBuilderProps> = ({ isOpen, onClose, editingRule }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [ruleName, setRuleName] = useState('')
  const [ruleDescription, setRuleDescription] = useState('')
  const [triggerType, setTriggerType] = useState<'new_quote' | 'newsletter_signup' | 'status_change' | 'time_based'>('new_quote')
  const [triggerConfig, setTriggerConfig] = useState<any>({})
  const [conditions, setConditions] = useState<Condition[]>([])
  const [actions, setActions] = useState<Action[]>([])
  const [webhookTemplate, setWebhookTemplate] = useState('')
  const [testData, setTestData] = useState('')

  const totalSteps = 4

  // Sample Liquid templates
  const sampleTemplates = {
    slack: `{
  "text": "🎯 New {{ quote.budget }} quote from {{ quote.name }}",
  "blocks": [
    {
      "type": "section",
      "text": {
        "type": "mrkdwn",
        "text": "*Project:* {{ quote.project_type }}\\n*Industry:* {{ quote.industry }}\\n*Timeline:* {{ quote.timeline }}"
      }
    },
    {
      "type": "actions",
      "elements": [
        {
          "type": "button",
          "text": {
            "type": "plain_text",
            "text": "View Quote"
          },
          "url": "https://yoursite.com/admin/quotes"
        }
      ]
    }
  ]
}`,
    ghl: `{
  "contact": {
    "name": "{{ quote.name }}",
    "email": "{{ quote.email }}",
    "phone": "{{ quote.phone }}",
    "company": "{{ quote.company }}"
  },
  "opportunity": {
    "title": "{{ quote.project_type }} - {{ quote.company | default: quote.name }}",
    "value": {% if quote.budget contains '$100,000+' %}100000{% elsif quote.budget contains '$50,000' %}75000{% else %}25000{% endif %},
    "stage": "quote_requested",
    "source": "Website Quote Form"
  },
  "custom_fields": {
    "project_type": "{{ quote.project_type }}",
    "industry": "{{ quote.industry }}",
    "timeline": "{{ quote.timeline }}",
    "features": "{{ quote.key_features | join: ', ' }}",
    "wants_blog_account": {{ quote.wants_account }}
  }
}`,
    custom: `{
  "customer_name": "{{ quote.name }}",
  "project_type": "{{ quote.project_type }}",
  "budget": "{{ quote.budget }}",
  "email": "{{ quote.email }}",
  "features": [
    {% for feature in quote.key_features %}
      "{{ feature }}"{% unless forloop.last %},{% endunless %}
    {% endfor %}
  ],
  "is_high_value": {% if quote.budget contains '$100,000+' %}true{% else %}false{% endif %},
  "created_date": "{{ quote.created_at | date: '%Y-%m-%d' }}"
}`
  }

  const addCondition = () => {
    setConditions([...conditions, { field: 'budget', operator: 'contains', value: '' }])
  }

  const removeCondition = (index: number) => {
    setConditions(conditions.filter((_, i) => i !== index))
  }

  const updateCondition = (index: number, field: keyof Condition, value: string) => {
    const updated = [...conditions]
    updated[index] = { ...updated[index], [field]: value }
    setConditions(updated)
  }

  const addAction = () => {
    setActions([...actions, { 
      type: 'webhook', 
      name: 'New Webhook',
      config: { url: '', method: 'POST', template: '' }
    }])
  }

  const removeAction = (index: number) => {
    setActions(actions.filter((_, i) => i !== index))
  }

  const updateAction = (index: number, field: string, value: any) => {
    const updated = [...actions]
    updated[index] = { ...updated[index], [field]: value }
    setActions(updated)
  }

  const handleSave = async () => {
    if (!ruleName.trim()) {
      toast.error('Please enter a rule name')
      return
    }

    if (actions.length === 0) {
      toast.error('Please add at least one action')
      return
    }

    // TODO: Implement save logic
    toast.success('Automation rule saved successfully!')
    onClose()
  }

  const handleTest = async () => {
    // TODO: Implement test logic
    toast.success('Test webhook sent successfully!')
  }

  const loadTemplate = (template: string) => {
    setWebhookTemplate(sampleTemplates[template as keyof typeof sampleTemplates])
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-montserrat">
            {editingRule ? 'Edit Automation' : 'Create New Automation'}
          </DialogTitle>
          <div className="flex items-center gap-2 mt-2">
            {[1, 2, 3, 4].map((step) => (
              <div
                key={step}
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step <= currentStep
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                }`}
              >
                {step}
              </div>
            ))}
          </div>
        </DialogHeader>

        <div className="mt-6">
          {/* Step 1: Basic Info */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Settings className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Basic Information</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Give your automation a name and description
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <Label>Automation Name *</Label>
                  <Input
                    value={ruleName}
                    onChange={(e) => setRuleName(e.target.value)}
                    placeholder="e.g., High-Value Lead Alert"
                  />
                </div>

                <div>
                  <Label>Description</Label>
                  <Textarea
                    value={ruleDescription}
                    onChange={(e) => setRuleDescription(e.target.value)}
                    placeholder="Describe what this automation does..."
                    rows={3}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Trigger */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Zap className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Choose Trigger</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  When should this automation run?
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {[
                  { value: 'new_quote', label: 'New Quote', icon: Zap, desc: 'When a new quote is submitted' },
                  { value: 'newsletter_signup', label: 'Newsletter Signup', icon: Mail, desc: 'When someone subscribes to newsletter' },
                  { value: 'status_change', label: 'Status Change', icon: Settings, desc: 'When quote status changes' },
                  { value: 'time_based', label: 'Time Based', icon: Clock, desc: 'On a schedule or delay' }
                ].map((trigger) => (
                  <Card
                    key={trigger.value}
                    className={`cursor-pointer transition-all ${
                      triggerType === trigger.value
                        ? 'ring-2 ring-primary bg-primary/5'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    onClick={() => setTriggerType(trigger.value as any)}
                  >
                    <CardContent className="p-6 text-center">
                      <trigger.icon className="h-8 w-8 mx-auto mb-3 text-primary" />
                      <h4 className="font-semibold mb-2">{trigger.label}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {trigger.desc}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {triggerType === 'status_change' && (
                <div className="mt-6">
                  <Label>Status Change</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status change" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new_to_reviewed">New → Reviewed</SelectItem>
                      <SelectItem value="reviewed_to_quoted">Reviewed → Quoted</SelectItem>
                      <SelectItem value="quoted_to_converted">Quoted → Converted</SelectItem>
                      <SelectItem value="any_status">Any Status Change</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {triggerType === 'time_based' && (
                <div className="mt-6 space-y-4">
                  <div>
                    <Label>Delay After Quote</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select delay" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1h">1 Hour</SelectItem>
                        <SelectItem value="24h">24 Hours</SelectItem>
                        <SelectItem value="3d">3 Days</SelectItem>
                        <SelectItem value="1w">1 Week</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Conditions */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Settings className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Add Conditions</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Optional: Only run when certain conditions are met
                </p>
              </div>

              <div className="space-y-4">
                {conditions.map((condition, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="flex items-center gap-4">
                        <Select
                          value={condition.field}
                          onValueChange={(value) => updateCondition(index, 'field', value)}
                        >
                          <SelectTrigger className="w-40">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="budget">Budget</SelectItem>
                            <SelectItem value="industry">Industry</SelectItem>
                            <SelectItem value="project_type">Project Type</SelectItem>
                            <SelectItem value="timeline">Timeline</SelectItem>
                          </SelectContent>
                        </Select>

                        <Select
                          value={condition.operator}
                          onValueChange={(value) => updateCondition(index, 'operator', value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="contains">Contains</SelectItem>
                            <SelectItem value="equals">Equals</SelectItem>
                            <SelectItem value="not_equals">Not Equals</SelectItem>
                          </SelectContent>
                        </Select>

                        <Input
                          value={condition.value}
                          onChange={(e) => updateCondition(index, 'value', e.target.value)}
                          placeholder="Value"
                          className="flex-1"
                        />

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCondition(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}

                <Button variant="outline" onClick={addCondition} className="w-full">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Condition
                </Button>
              </div>
            </div>
          )}

          {/* Step 4: Actions & Templates */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <Webhook className="h-12 w-12 text-primary mx-auto mb-3" />
                <h3 className="text-xl font-semibold">Configure Actions</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  What should happen when this automation triggers?
                </p>
              </div>

              <Tabs defaultValue="webhook" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="webhook">Webhook</TabsTrigger>
                  <TabsTrigger value="template">Template</TabsTrigger>
                  <TabsTrigger value="test">Test</TabsTrigger>
                </TabsList>

                <TabsContent value="webhook" className="space-y-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg mb-4">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      🔄 Reverse Webhook Strategy
                    </h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
                      Instead of sending webhooks TO external services, provide webhook URLs that external services can call.
                    </p>
                    <div className="space-y-2">
                      <div className="text-xs font-mono bg-white dark:bg-gray-800 p-2 rounded border">
                        <strong>Your Webhook URL:</strong><br/>
                        https://your-project.supabase.co/functions/v1/quote-webhook
                      </div>
                      <p className="text-xs text-blue-700 dark:text-blue-300">
                        Use this URL in GHL automation to receive quote data and trigger contact creation.
                      </p>
                    </div>
                  </div>

                  <div>
                    <Label>External Webhook URL (Optional)</Label>
                    <Input
                      placeholder="https://hooks.slack.com/services/... or GHL webhook URL"
                      className="font-mono text-sm"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      URL to send webhook data to (Slack, Zapier, etc.)
                    </p>
                  </div>

                  <div>
                    <Label>HTTP Method</Label>
                    <Select defaultValue="POST">
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="POST">POST</SelectItem>
                        <SelectItem value="PUT">PUT</SelectItem>
                        <SelectItem value="PATCH">PATCH</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Headers (Optional)</Label>
                    <Textarea
                      placeholder={`{
  "Content-Type": "application/json",
  "Authorization": "Bearer your-token"
}`}
                      rows={4}
                      className="font-mono text-sm"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="template" className="space-y-4">
                  <div className="flex gap-2 mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTemplate('slack')}
                    >
                      Slack Template
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTemplate('ghl')}
                    >
                      GoHighLevel
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => loadTemplate('custom')}
                    >
                      Custom JSON
                    </Button>
                  </div>

                  <div>
                    <Label>Liquid Template *</Label>
                    <Textarea
                      value={webhookTemplate}
                      onChange={(e) => setWebhookTemplate(e.target.value)}
                      placeholder="Enter your Liquid template here..."
                      rows={12}
                      className="font-mono text-sm"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Use Liquid syntax: {`{{ quote.name }}`}, {`{% if quote.budget contains '$100,000+' %}`}
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="test" className="space-y-4">
                  <div>
                    <Label>Test with Sample Data</Label>
                    <Textarea
                      value={testData}
                      onChange={(e) => setTestData(e.target.value)}
                      placeholder="Sample quote data will be loaded here..."
                      rows={8}
                      className="font-mono text-sm"
                    />
                  </div>

                  <Button onClick={handleTest} className="w-full">
                    <TestTube className="h-4 w-4 mr-2" />
                    Send Test Webhook
                  </Button>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
            >
              Previous
            </Button>

            <div className="flex gap-2">
              {currentStep < totalSteps ? (
                <Button
                  onClick={() => setCurrentStep(Math.min(totalSteps, currentStep + 1))}
                  disabled={currentStep === 1 && !ruleName.trim()}
                >
                  Next
                </Button>
              ) : (
                <Button onClick={handleSave}>
                  <Save className="h-4 w-4 mr-2" />
                  Save Automation
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default AutomationBuilder
