import React, { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Quote, Star, Plus, Filter, ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { getTestimonials, getDisplayAuthor, BlogPost } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'

const Testimonials = () => {
  const [testimonials, setTestimonials] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<string>('all')
  const { user } = useAuth()

  const projectTypes = [
    { value: 'all', label: 'All Projects' },
    { value: 'mvp-development', label: 'MVP Development' },
    { value: 'custom-web-solutions', label: 'Custom Web Solutions' },
    { value: 'landing-pages', label: 'Landing Pages' },
    { value: 'saas-platforms', label: 'SaaS Platforms' },
    { value: 'digital-strategy', label: 'Digital Strategy' }
  ]

  useEffect(() => {
    fetchTestimonials()
  }, [])

  const fetchTestimonials = async () => {
    try {
      const { data, error } = await getTestimonials()

      if (error) {
        console.error('Error fetching testimonials:', error)
        toast.error('Failed to load testimonials')
        return
      }

      setTestimonials(data || [])
    } catch (error) {
      console.error('Error fetching testimonials:', error)
      toast.error('Failed to load testimonials')
    } finally {
      setLoading(false)
    }
  }

  const filteredTestimonials = filter === 'all'
    ? testimonials
    : testimonials.filter(t => t.tags?.includes(filter))

  const extractRating = (content: string): number => {
    // Extract rating from content (look for star emojis and rating text)
    const ratingMatch = content.match(/⭐{1,5}.*?(\d+)\/5 stars/i) || content.match(/(\d+)\/5 stars/i)
    if (ratingMatch) {
      return parseInt(ratingMatch[1])
    }

    // Fallback: count star emojis
    const starMatch = content.match(/⭐{1,5}/g)
    if (starMatch) {
      return starMatch[0].length
    }

    return 5 // Default to 5 stars
  }

  const extractCompany = (content: string): string => {
    // Extract company from HTML content structure
    const companyMatch = content.match(/<h2>About\s+([^<]+)<\/h2>/i)
    return companyMatch ? companyMatch[1].trim() : 'Client'
  }

  const extractProjectType = (content: string): string => {
    // Extract project type from HTML content
    const projectMatch = content.match(/<p><strong>Project Type:<\/strong>\s*([^<]+)<\/p>/i)
    return projectMatch ? projectMatch[1].trim() : 'Project'
  }

  const extractTestimonialText = (content: string): string => {
    // Extract the main testimonial text from HTML content
    const experienceMatch = content.match(/<h2>My Experience with MBI<\/h2>\s*<p>([^<]+)/i)
    if (experienceMatch) {
      return experienceMatch[1].trim()
    }

    // Fallback: try to find any paragraph with substantial content
    const paragraphMatch = content.match(/<p>([^<]{50,})/i)
    if (paragraphMatch) {
      return paragraphMatch[1].trim()
    }

    return 'Great experience working with MBI!'
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-12 bg-gradient-to-b from-primary/5 to-transparent">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-montserrat font-bold text-4xl sm:text-5xl md:text-6xl text-gray-900 dark:text-white mb-6">
              Success Stories & <span className="text-transparent bg-gradient-to-r from-primary to-accent bg-clip-text">Testimonials</span>
            </h1>
            <p className="font-poppins text-lg sm:text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
              Real stories from real clients who've transformed their ideas into successful digital platforms with MBI.
            </p>
            
            {user ? (
              <Link to="/admin/blog/testimonial">
                <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 py-3">
                  <Plus className="w-4 h-4 mr-2" />
                  Share Your Story
                </Button>
              </Link>
            ) : (
              <Link to="/blog">
                <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-6 py-3">
                  <Plus className="w-4 h-4 mr-2" />
                  Join Blog & Share Your Story
                </Button>
              </Link>
            )}
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex flex-wrap items-center justify-center gap-4">
            <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
              <Filter className="w-4 h-4" />
              <span className="font-medium">Filter by project type:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {projectTypes.map((type) => (
                <Button
                  key={type.value}
                  variant={filter === type.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilter(type.value)}
                  className="font-poppins"
                >
                  {type.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4 sm:px-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                </div>
              ))}
            </div>
          ) : filteredTestimonials.length === 0 ? (
            <div className="text-center py-16">
              <Quote className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="font-montserrat font-semibold text-xl text-gray-600 dark:text-gray-400 mb-2">
                No testimonials found
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                {filter === 'all' ? 'Be the first to share your success story!' : 'Try a different filter or check back later.'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredTestimonials.map((testimonial) => {
                const displayAuthor = getDisplayAuthor(testimonial)
                const rating = extractRating(testimonial.content)
                const company = extractCompany(testimonial.content)
                const projectType = extractProjectType(testimonial.content)
                const testimonialText = extractTestimonialText(testimonial.content)

                return (
                  <Card
                    key={testimonial.id}
                    className="bg-white/80 dark:bg-white/5 border backdrop-blur-lg hover:shadow-xl transition-all duration-300 border-white/40 dark:border-white/10 cursor-pointer"
                    onClick={() => window.open(`/blog/${testimonial.slug}`, '_blank')}
                  >
                    <CardContent className="p-6">
                      {/* Success Story Badge */}
                      <Badge className="mb-4 bg-gradient-to-r from-primary to-accent text-white">
                        Success Story
                      </Badge>

                      {/* Rating */}
                      <div className="flex items-center justify-between mb-4">
                        {renderStars(rating)}
                        <Badge variant="outline" className="text-xs">
                          {projectType}
                        </Badge>
                      </div>

                      {/* Quote */}
                      <blockquote className="text-gray-700 dark:text-gray-200 font-poppins leading-relaxed mb-6 italic">
                        "{testimonialText.length > 150 ? testimonialText.substring(0, 150) + '...' : testimonialText}"
                      </blockquote>

                      {/* Author */}
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div className="font-montserrat font-semibold text-gray-900 dark:text-white">
                          {displayAuthor.name}
                        </div>
                        <div className="text-sm text-primary font-medium">
                          {company}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          Click to read full story →
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-primary/10 to-accent/10">
        <div className="container mx-auto px-4 sm:px-6 text-center">
          <h2 className="font-montserrat font-bold text-3xl sm:text-4xl text-gray-900 dark:text-white mb-6">
            Ready to Join Our Success Stories?
          </h2>
          <p className="font-poppins text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
            Transform your startup idea into a scalable, profitable platform that attracts investors and delights users.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/#contact">
              <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/80 hover:to-accent/80 text-white font-poppins px-8 py-3 text-lg">
                Start Your Project
                <ArrowRight className="ml-2 w-4 h-4" />
              </Button>
            </Link>
            <Link to="/case-studies">
              <Button variant="outline" className="border-primary text-primary hover:bg-primary/10 font-poppins px-8 py-3 text-lg">
                View Case Studies
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default Testimonials
