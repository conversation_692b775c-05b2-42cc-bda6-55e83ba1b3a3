import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Plus,
  ChevronUp,
  MessageCircle,
  Filter,
  Search,
  Lightbulb,
  Plug,
  Bug,
  TrendingUp,
  Clock,
  PlayCircle,
  CheckCircle,
  PauseCircle,
  XCircle,
  GripVertical
} from 'lucide-react'
import { toast } from 'sonner'
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  useDroppable,
} from '@dnd-kit/core'
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  CSS,
} from '@dnd-kit/utilities'
import {
  getFeatureRequestBoards,
  getFeatureRequestStatuses,
  getFeatureRequests,
  createFeatureRequest,
  toggleFeatureRequestUpvote,
  getUserUpvote,
  updateFeatureRequest
} from '@/lib/supabase'

interface Board {
  id: string
  name: string
  slug: string
  color: string
  icon: string
  description?: string
}

interface Status {
  id: string
  name: string
  slug: string
  color: string
  icon: string
}

interface FeatureRequest {
  id: string
  title: string
  description: string
  upvotes_count: number
  comments_count: number
  priority: string
  created_at: string
  board: Board
  status?: Status
  author?: {
    full_name: string
    avatar_url?: string
  }
  author_name?: string
}

// Sortable item component for drag and drop
interface SortableRequestItemProps {
  request: FeatureRequest
  userUpvotes: Set<string>
  onUpvote: (requestId: string) => void
  canDrag: boolean
}

const SortableRequestItem: React.FC<SortableRequestItemProps> = ({
  request,
  userUpvotes,
  onUpvote,
  canDrag
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: request.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg ${
        canDrag ? 'cursor-grab active:cursor-grabbing' : ''
      } ${isDragging ? 'shadow-lg' : ''}`}
      {...(canDrag ? attributes : {})}
    >
      {canDrag && (
        <div {...listeners} className="cursor-grab active:cursor-grabbing">
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>
      )}

      <div className="flex items-center gap-2">
        <button
          onClick={() => onUpvote(request.id)}
          className={`flex items-center gap-1 px-2 py-1 rounded text-sm ${
            userUpvotes.has(request.id)
              ? 'bg-primary text-white'
              : 'bg-white dark:bg-gray-700 border'
          }`}
        >
          <ChevronUp className="h-3 w-3" />
          {request.upvotes_count}
        </button>
      </div>

      <div className="flex-1">
        <h4 className="font-medium text-gray-900 dark:text-white">
          {request.title}
        </h4>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {request.board.name}
        </p>
      </div>
    </div>
  )
}

// Droppable status container
interface DroppableStatusContainerProps {
  status: Status
  children: React.ReactNode
  canDrag: boolean
  isDragging: boolean
}

const DroppableStatusContainer: React.FC<DroppableStatusContainerProps> = ({
  status,
  children,
  canDrag,
  isDragging
}) => {
  const { isOver, setNodeRef } = useDroppable({
    id: status.id,
  })

  return (
    <div
      ref={setNodeRef}
      className={`min-h-[100px] ${canDrag ? 'border-2 border-dashed border-transparent rounded-lg p-2' : ''} ${
        isOver && canDrag ? 'border-primary bg-primary/10' : ''
      } ${isDragging && canDrag ? 'border-primary/30 bg-primary/5' : ''}`}
    >
      {children}
    </div>
  )
}

const FeatureRequests = () => {
  const { user, profile } = useAuth()
  const [boards, setBoards] = useState<Board[]>([])
  const [statuses, setStatuses] = useState<Status[]>([])
  const [requests, setRequests] = useState<FeatureRequest[]>([])
  const [filteredRequests, setFilteredRequests] = useState<FeatureRequest[]>([])
  const [selectedBoard, setSelectedBoard] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [userUpvotes, setUserUpvotes] = useState<Set<string>>(new Set())
  const [activeId, setActiveId] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)

  // Check if user can drag (owner or super_admin)
  const canDrag = profile?.role === 'owner' || profile?.role === 'super_admin'

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  )

  // Create form state
  const [newRequest, setNewRequest] = useState({
    board_id: '',
    title: '',
    description: '',
    priority: 'medium',
    author_email: '',
    author_name: ''
  })

  useEffect(() => {
    loadData()
  }, [])

  useEffect(() => {
    filterRequests()
  }, [requests, selectedBoard, selectedStatus, searchQuery])

  const loadData = async () => {
    try {
      const [boardsRes, statusesRes, requestsRes] = await Promise.all([
        getFeatureRequestBoards(),
        getFeatureRequestStatuses(),
        getFeatureRequests()
      ])

      if (boardsRes.data) setBoards(boardsRes.data)
      if (statusesRes.data) setStatuses(statusesRes.data)
      if (requestsRes.data) {
        setRequests(requestsRes.data)
        await loadUserUpvotes(requestsRes.data)
      }
    } catch (error) {
      toast.error('Failed to load feature requests')
    } finally {
      setLoading(false)
    }
  }

  const loadUserUpvotes = async (requestList: FeatureRequest[]) => {
    if (!user && !localStorage.getItem('guest_email')) return

    const upvotePromises = requestList.map(async (request) => {
      const { data } = await getUserUpvote(
        request.id, 
        user?.email || localStorage.getItem('guest_email') || undefined
      )
      return { requestId: request.id, hasUpvoted: data }
    })

    const upvoteResults = await Promise.all(upvotePromises)
    const upvotedIds = new Set(
      upvoteResults.filter(result => result.hasUpvoted).map(result => result.requestId)
    )
    setUserUpvotes(upvotedIds)
  }

  const filterRequests = () => {
    let filtered = requests

    if (selectedBoard !== 'all') {
      filtered = filtered.filter(req => req.board.id === selectedBoard)
    }

    if (selectedStatus !== 'all') {
      filtered = filtered.filter(req => req.status?.id === selectedStatus)
    }

    if (searchQuery) {
      filtered = filtered.filter(req => 
        req.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        req.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredRequests(filtered)
  }

  const handleCreateRequest = async () => {
    if (!newRequest.title.trim() || !newRequest.description.trim() || !newRequest.board_id) {
      toast.error('Please fill in all required fields')
      return
    }

    if (!user && (!newRequest.author_email || !newRequest.author_name)) {
      toast.error('Please provide your email and name')
      return
    }

    try {
      const { data, error } = await createFeatureRequest(newRequest)
      if (error) throw error

      if (data) {
        setRequests(prev => [data, ...prev])
        setShowCreateModal(false)
        setNewRequest({
          board_id: '',
          title: '',
          description: '',
          priority: 'medium',
          author_email: '',
          author_name: ''
        })
        toast.success('Feature request created successfully!')
      }
    } catch (error) {
      toast.error('Failed to create feature request')
    }
  }

  const handleUpvote = async (requestId: string) => {
    try {
      let voterEmail = user?.email
      let voterName = user?.user_metadata?.full_name

      if (!user) {
        const guestEmail = localStorage.getItem('guest_email')
        const guestName = localStorage.getItem('guest_name')
        
        if (!guestEmail) {
          const email = prompt('Enter your email to vote:')
          const name = prompt('Enter your name:')
          if (!email || !name) return
          
          localStorage.setItem('guest_email', email)
          localStorage.setItem('guest_name', name)
          voterEmail = email
          voterName = name
        } else {
          voterEmail = guestEmail
          voterName = guestName
        }
      }

      const { data, error } = await toggleFeatureRequestUpvote(requestId, voterEmail, voterName)
      if (error) throw error

      const wasAdded = data?.action === 'added'
      
      // Update local state
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { ...req, upvotes_count: req.upvotes_count + (wasAdded ? 1 : -1) }
          : req
      ))

      // Update user upvotes
      setUserUpvotes(prev => {
        const newSet = new Set(prev)
        if (wasAdded) {
          newSet.add(requestId)
        } else {
          newSet.delete(requestId)
        }
        return newSet
      })

      toast.success(wasAdded ? 'Upvoted!' : 'Upvote removed')
    } catch (error) {
      toast.error('Failed to update vote')
    }
  }

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string)
    setIsDragging(true)
  }

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event
    setActiveId(null)
    setIsDragging(false)

    if (!over || !canDrag) return

    const activeId = active.id as string
    const overId = over.id as string

    // Check if we're dropping on a status container
    const targetStatus = statuses.find(status => status.id === overId)
    if (!targetStatus) return

    const draggedRequest = requests.find(req => req.id === activeId)
    if (!draggedRequest || draggedRequest.status?.id === targetStatus.id) return

    try {
      // Update the request status in the database
      const { data, error } = await updateFeatureRequest(activeId, {
        status_id: targetStatus.id
      })

      if (error) throw error

      // Update local state
      setRequests(prev => prev.map(req =>
        req.id === activeId
          ? { ...req, status: targetStatus }
          : req
      ))

      toast.success(`Moved to ${targetStatus.name}`)
    } catch (error) {
      console.error('Failed to update request status:', error)
      toast.error('Failed to move request')
    }
  }

  const getIconComponent = (iconName: string) => {
    const icons: { [key: string]: React.ComponentType<any> } = {
      lightbulb: Lightbulb,
      plug: Plug,
      bug: Bug,
      'trending-up': TrendingUp,
      clock: Clock,
      'play-circle': PlayCircle,
      'check-circle': CheckCircle,
      'pause-circle': PauseCircle,
      'x-circle': XCircle
    }
    return icons[iconName] || Lightbulb
  }

  const getBoardCounts = () => {
    const counts: { [key: string]: number } = {}
    boards.forEach(board => {
      counts[board.id] = requests.filter(req => req.board.id === board.id).length
    })
    return counts
  }

  const getStatusCounts = () => {
    const counts: { [key: string]: number } = {}
    statuses.forEach(status => {
      counts[status.id] = requests.filter(req => req.status?.id === status.id).length
    })
    return counts
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center space-y-4">
          <Lightbulb className="h-8 w-8 animate-pulse mx-auto text-primary" />
          <p className="text-gray-600 dark:text-gray-400">Loading feature requests...</p>
        </div>
      </div>
    )
  }

  const boardCounts = getBoardCounts()
  const statusCounts = getStatusCounts()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Feature Requests</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Share your ideas and vote on features you'd like to see
          </p>
        </div>
        
        <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
          <DialogTrigger asChild>
            <Button className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90">
              <Plus className="h-4 w-4 mr-2" />
              Create Request
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create Feature Request</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Board</label>
                <Select value={newRequest.board_id} onValueChange={(value) => 
                  setNewRequest(prev => ({ ...prev, board_id: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a board" />
                  </SelectTrigger>
                  <SelectContent>
                    {boards.map(board => {
                      const IconComponent = getIconComponent(board.icon)
                      return (
                        <SelectItem key={board.id} value={board.id}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" style={{ color: board.color }} />
                            {board.name}
                          </div>
                        </SelectItem>
                      )
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium">Title</label>
                <Input
                  value={newRequest.title}
                  onChange={(e) => setNewRequest(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Brief description of your request"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={newRequest.description}
                  onChange={(e) => setNewRequest(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Detailed description of what you'd like to see..."
                  rows={4}
                />
              </div>

              {!user && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Your Email</label>
                    <Input
                      type="email"
                      value={newRequest.author_email}
                      onChange={(e) => setNewRequest(prev => ({ ...prev, author_email: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Your Name</label>
                    <Input
                      value={newRequest.author_name}
                      onChange={(e) => setNewRequest(prev => ({ ...prev, author_name: e.target.value }))}
                      placeholder="Your name"
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateRequest}>
                  Create Request
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Boards and Filters */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Boards Sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Boards</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <button
                onClick={() => setSelectedBoard('all')}
                className={`w-full text-left p-3 rounded-lg transition-colors ${
                  selectedBoard === 'all'
                    ? 'bg-primary/10 text-primary border border-primary/20'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium">All Boards</span>
                  <span className="text-sm text-gray-500">{requests.length}</span>
                </div>
              </button>

              {boards.map(board => {
                const IconComponent = getIconComponent(board.icon)
                return (
                  <button
                    key={board.id}
                    onClick={() => setSelectedBoard(board.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      selectedBoard === board.id
                        ? 'border border-opacity-20'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    }`}
                    style={{
                      backgroundColor: selectedBoard === board.id ? `${board.color}10` : undefined,
                      color: selectedBoard === board.id ? board.color : undefined,
                      borderColor: selectedBoard === board.id ? `${board.color}40` : undefined
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" style={{ color: board.color }} />
                        <span className="font-medium">{board.name}</span>
                      </div>
                      <span className="text-sm opacity-70">{boardCounts[board.id] || 0}</span>
                    </div>
                  </button>
                )
              })}
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search feature requests..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                {statuses.map(status => {
                  const IconComponent = getIconComponent(status.icon)
                  return (
                    <SelectItem key={status.id} value={status.id}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" style={{ color: status.color }} />
                        {status.name}
                        <span className="text-xs text-gray-500">({statusCounts[status.id] || 0})</span>
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Roadmap Tabs */}
          <Tabs defaultValue="requests" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="requests">All Requests</TabsTrigger>
              <TabsTrigger value="roadmap">Roadmap</TabsTrigger>
            </TabsList>

            <TabsContent value="requests" className="space-y-4">
              {filteredRequests.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Lightbulb className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      No feature requests found
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      {searchQuery || selectedBoard !== 'all' || selectedStatus !== 'all'
                        ? 'Try adjusting your filters or search terms.'
                        : 'Be the first to suggest a new feature!'}
                    </p>
                    <Button onClick={() => setShowCreateModal(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create First Request
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                filteredRequests.map(request => (
                  <Card key={request.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex gap-4">
                        {/* Upvote Button */}
                        <div className="flex flex-col items-center">
                          <button
                            onClick={() => handleUpvote(request.id)}
                            className={`flex flex-col items-center p-2 rounded-lg transition-colors ${
                              userUpvotes.has(request.id)
                                ? 'bg-primary text-white'
                                : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                            }`}
                          >
                            <ChevronUp className="h-4 w-4" />
                            <span className="text-sm font-medium">{request.upvotes_count}</span>
                          </button>
                        </div>

                        {/* Content */}
                        <div className="flex-1">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {request.title}
                            </h3>
                            <div className="flex items-center gap-2">
                              {request.status && (
                                <Badge
                                  variant="secondary"
                                  style={{
                                    backgroundColor: `${request.status.color}20`,
                                    color: request.status.color,
                                    borderColor: `${request.status.color}40`
                                  }}
                                >
                                  {(() => {
                                    const IconComponent = getIconComponent(request.status.icon)
                                    return <IconComponent className="h-3 w-3 mr-1" />
                                  })()}
                                  {request.status.name}
                                </Badge>
                              )}
                              <Badge
                                variant="outline"
                                style={{
                                  backgroundColor: `${request.board.color}10`,
                                  color: request.board.color,
                                  borderColor: `${request.board.color}40`
                                }}
                              >
                                {(() => {
                                  const IconComponent = getIconComponent(request.board.icon)
                                  return <IconComponent className="h-3 w-3 mr-1" />
                                })()}
                                {request.board.name}
                              </Badge>
                            </div>
                          </div>

                          <p className="text-gray-600 dark:text-gray-400 mb-4 line-clamp-3">
                            {request.description}
                          </p>

                          <div className="flex items-center justify-between text-sm text-gray-500">
                            <div className="flex items-center gap-4">
                              <span>
                                by {request.author?.full_name || request.author_name || 'Anonymous'}
                              </span>
                              <span>
                                {new Date(request.created_at).toLocaleDateString()}
                              </span>
                            </div>

                            <div className="flex items-center gap-2">
                              <MessageCircle className="h-4 w-4" />
                              <span>{request.comments_count}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </TabsContent>

            <TabsContent value="roadmap" className="space-y-6">
              {canDrag && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                  <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                    <GripVertical className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      Drag and drop enabled - You can move requests between statuses
                    </span>
                  </div>
                </div>
              )}

              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
              >
                {/* Unassigned Requests Section (for owners/super admins) */}
                {canDrag && (() => {
                  const unassignedRequests = filteredRequests.filter(req => !req.status)
                  if (unassignedRequests.length > 0) {
                    return (
                      <Card className="border-dashed border-2 border-gray-300 dark:border-gray-600">
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                            <Clock className="h-5 w-5" />
                            Unassigned Requests
                            <Badge variant="secondary">{unassignedRequests.length}</Badge>
                            <Badge variant="outline" className="text-xs">
                              Drag to Status
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <SortableContext
                            items={unassignedRequests.map(req => req.id)}
                            strategy={verticalListSortingStrategy}
                          >
                            <div className="space-y-3">
                              {unassignedRequests.map(request => (
                                <SortableRequestItem
                                  key={request.id}
                                  request={request}
                                  userUpvotes={userUpvotes}
                                  onUpvote={handleUpvote}
                                  canDrag={canDrag}
                                />
                              ))}
                            </div>
                          </SortableContext>
                        </CardContent>
                      </Card>
                    )
                  }
                  return null
                })()}

                {statuses.map(status => {
                  const statusRequests = filteredRequests.filter(req => req.status?.id === status.id)
                  const IconComponent = getIconComponent(status.icon)

                  return (
                    <Card
                      key={status.id}
                      className={`${canDrag ? 'transition-colors' : ''} ${
                        isDragging ? 'ring-2 ring-primary/20' : ''
                      }`}
                    >
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <IconComponent className="h-5 w-5" style={{ color: status.color }} />
                          {status.name}
                          <Badge variant="secondary">{statusRequests.length}</Badge>
                          {canDrag && (
                            <Badge variant="outline" className="text-xs">
                              Drop Zone
                            </Badge>
                          )}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <DroppableStatusContainer
                          status={status}
                          canDrag={canDrag}
                          isDragging={!!activeId}
                        >
                          {statusRequests.length === 0 ? (
                            <p className="text-gray-500 text-center py-8">
                              {canDrag ? 'Drop requests here' : 'No requests in this status'}
                            </p>
                          ) : (
                            <SortableContext
                              items={statusRequests.map(req => req.id)}
                              strategy={verticalListSortingStrategy}
                            >
                              <div className="space-y-3">
                                {statusRequests.map(request => (
                                  <SortableRequestItem
                                    key={request.id}
                                    request={request}
                                    userUpvotes={userUpvotes}
                                    onUpvote={handleUpvote}
                                    canDrag={canDrag}
                                  />
                                ))}
                              </div>
                            </SortableContext>
                          )}
                        </DroppableStatusContainer>
                      </CardContent>
                    </Card>
                  )
                })}

                <DragOverlay>
                  {activeId ? (
                    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-lg">
                      <div className="flex items-center gap-3">
                        <GripVertical className="h-4 w-4 text-gray-400" />
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {requests.find(req => req.id === activeId)?.title}
                          </h4>
                        </div>
                      </div>
                    </div>
                  ) : null}
                </DragOverlay>
              </DndContext>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

export default FeatureRequests
