import React, { useState, useRef, useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { useOrganization } from '@/contexts/OrganizationContext'
import {
  MessageCircle,
  X,
  Send,
  Loader2,
  Sparkles,
  HelpCircle,
  Zap,
  FileText,
  Settings
} from 'lucide-react'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface Message {
  id: string
  content: string
  isUser: boolean
  timestamp: Date
}

const AIAssistant = () => {
  const { user, profile } = useAuth()
  const { currentOrganization } = useOrganization()
  const location = useLocation()
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Get contextual welcome message based on current page
  const getWelcomeMessage = () => {
    const path = location.pathname
    let contextualHelp = ''

    if (path.includes('/blog')) {
      contextualHelp = '\n\n🎯 I see you\'re working on blog content! I can help you write, edit, or brainstorm ideas.'
    } else if (path.includes('/automations') || path.includes('/workflow')) {
      contextualHelp = '\n\n⚡ Working on workflows? I can help you set up automations, triggers, and actions.'
    } else if (path.includes('/email-integrations')) {
      contextualHelp = '\n\n📧 Setting up email integrations? I can guide you through connecting your email providers.'
    } else if (path.includes('/credits')) {
      contextualHelp = '\n\n💳 Checking your usage? I can explain your credit limits and help you optimize usage.'
    }

    return `Hi ${profile?.full_name || 'there'}! 👋 I'm your AI assistant. I can help you with:\n\n• Creating blog posts and content\n• Setting up workflows and automations\n• Navigating the platform\n• General questions about MBI features${contextualHelp}\n\nWhat would you like help with today?`
  }

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setMessages([{
        id: '1',
        content: getWelcomeMessage(),
        isUser: false,
        timestamp: new Date()
      }])
    }
  }, [isOpen, profile?.full_name, location.pathname])

  const quickActions = [
    { icon: FileText, label: 'Write a blog post', prompt: 'Help me write a blog post about' },
    { icon: Zap, label: 'Create workflow', prompt: 'Help me create a workflow for' },
    { icon: Settings, label: 'Platform help', prompt: 'How do I' },
    { icon: HelpCircle, label: 'General question', prompt: 'I have a question about' }
  ]

  const handleQuickAction = (prompt: string) => {
    setInputValue(prompt + ' ')
  }

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isUser: true,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      // Simulate AI response for now
      // In production, this would call your AI service
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        content: getAIResponse(userMessage.content),
        isUser: false,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiResponse])
    } catch (error) {
      toast.error('Failed to get AI response. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Simple AI response simulation - replace with actual AI service
  const getAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase()
    
    if (input.includes('blog') || input.includes('write') || input.includes('content')) {
      return "I'd be happy to help you create content! Here are some tips:\n\n• Start with a compelling headline\n• Use the rich text editor in Admin → New Post\n• Add images to make your content engaging\n• Use the AI blog generation feature for inspiration\n\nWould you like me to help you brainstorm topics or structure your post?"
    }
    
    if (input.includes('workflow') || input.includes('automation')) {
      return "Great! Workflows can automate many tasks. Here's how to get started:\n\n• Go to Admin → Workflows\n• Click 'Create Workflow'\n• Choose a trigger (form submit, manual, webhook)\n• Add actions (send email, webhook, delay)\n• Test your workflow before going live\n\nWhat kind of automation are you looking to create?"
    }
    
    if (input.includes('email') || input.includes('integration')) {
      return "Email integrations let you send automated emails! Here's the process:\n\n• Go to Admin → Email Integrations\n• Add your email provider (Gmail, Outlook, etc.)\n• Test the connection\n• Use it in your workflows\n\nNeed help setting up a specific email provider?"
    }
    
    if (input.includes('credits') || input.includes('usage') || input.includes('limit')) {
      return `You're on the ${currentOrganization?.subscription_plan || 'free'} plan. Here's what you have:\n\n• Workflow Credits: ${currentOrganization?.workflow_credits_limit || 100} per month\n• AI Credits: ${currentOrganization?.ai_credits_limit || 50} per month\n\nYou can check your usage in Admin → Credits Usage. Need to upgrade your plan?`
    }
    
    return "I'm here to help! I can assist with:\n\n• Content creation and blog writing\n• Setting up workflows and automations\n• Email integrations and setup\n• Platform navigation and features\n• General questions about MBI\n\nCould you be more specific about what you'd like help with?"
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Only show on admin pages and for authenticated users
  if (!user || !location.pathname.startsWith('/admin')) return null

  return (
    <>
      {/* Floating AI Assistant Button */}
      <div className="fixed bottom-6 right-6 z-50">
        {!isOpen && (
          <Button
            onClick={() => setIsOpen(true)}
            className="h-14 w-14 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 shadow-lg hover:shadow-xl transition-all duration-300 group"
          >
            <div className="relative">
              <Sparkles className="h-6 w-6 text-white" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </Button>
        )}
      </div>

      {/* AI Assistant Chat Window */}
      {isOpen && (
        <div className="fixed bottom-6 right-6 z-50 w-96 h-[500px] bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-500 to-blue-500 rounded-t-lg">
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-white" />
              <h3 className="font-semibold text-white">AI Assistant</h3>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                Beta
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-white/20"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Quick Actions */}
          {messages.length <= 1 && (
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-2">
                {quickActions.map((action, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAction(action.prompt)}
                    className="flex items-center gap-1 text-xs h-8"
                  >
                    <action.icon className="h-3 w-3" />
                    {action.label}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex',
                  message.isUser ? 'justify-end' : 'justify-start'
                )}
              >
                <div
                  className={cn(
                    'max-w-[80%] rounded-lg p-3 text-sm whitespace-pre-wrap',
                    message.isUser
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                  )}
                >
                  {message.content}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">Thinking...</span>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex gap-2">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me anything..."
                disabled={isLoading}
                className="flex-1"
              />
              <Button
                onClick={handleSendMessage}
                disabled={!inputValue.trim() || isLoading}
                size="sm"
                className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default AIAssistant
