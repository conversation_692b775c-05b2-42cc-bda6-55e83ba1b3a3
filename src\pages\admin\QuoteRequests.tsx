import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { getQuoteRequests, updateQuoteRequestStatus, QuoteRequest } from '@/lib/quote-requests'
import { ArrowLeft, Mail, Phone, Calendar, DollarSign, Users, Briefcase } from 'lucide-react'
import { toast } from 'sonner'

const QuoteRequests = () => {
  const { profile } = useAuth()
  const [quotes, setQuotes] = useState<QuoteRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<string>('all')

  useEffect(() => {
    fetchQuotes()
  }, [])

  const fetchQuotes = async () => {
    setLoading(true)
    try {
      const { data, error } = await getQuoteRequests()
      if (error) {
        toast.error('Failed to load quote requests')
        console.error('Error fetching quotes:', error)
      } else {
        setQuotes(data || [])
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleStatusUpdate = async (id: string, status: QuoteRequest['status']) => {
    try {
      const { error } = await updateQuoteRequestStatus(id, status)
      if (error) {
        toast.error('Failed to update status')
        console.error('Error updating status:', error)
      } else {
        toast.success('Status updated successfully')
        fetchQuotes() // Refresh the list
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Error:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'reviewed': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'quoted': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'converted': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'declined': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const filteredQuotes = quotes.filter(quote => 
    filter === 'all' || quote.status === filter
  )

  // Check if user has admin permissions
  if (!profile || !['owner', 'super_admin', 'admin', 'saas_owner'].includes(profile.role)) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Card className="max-w-md">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You don't have permission to view quote requests.
              </p>
              <Link to="/admin/blog">
                <Button>Go to Dashboard</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
          {/* Header */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
            <div className="flex items-center gap-4">
              <Link to="/admin/blog">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <h1 className="text-xl sm:text-2xl font-bold">Quote Requests</h1>
            </div>

            <div className="flex items-center gap-2">
              <Select value={filter} onValueChange={setFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Requests</SelectItem>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="reviewed">Reviewed</SelectItem>
                  <SelectItem value="quoted">Quoted</SelectItem>
                  <SelectItem value="converted">Converted</SelectItem>
                  <SelectItem value="declined">Declined</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
            {['new', 'reviewed', 'quoted', 'converted', 'declined'].map(status => {
              const count = quotes.filter(q => q.status === status).length
              return (
                <Card key={status}>
                  <CardContent className="p-4 text-center">
                    <div className="text-2xl font-bold">{count}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                      {status}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Quote Requests List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600 dark:text-gray-400">Loading quote requests...</p>
            </div>
          ) : filteredQuotes.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Quote Requests</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {filter === 'all' 
                    ? "No quote requests have been submitted yet."
                    : `No ${filter} quote requests found.`
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              {filteredQuotes.map((quote) => (
                <Card key={quote.id} className="overflow-hidden">
                  <CardHeader className="pb-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div>
                        <CardTitle className="text-lg">{quote.name}</CardTitle>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {quote.company && `${quote.company} • `}
                          {new Date(quote.created_at!).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(quote.status)}>
                          {quote.status}
                        </Badge>
                        <Select
                          value={quote.status}
                          onValueChange={(status) => handleStatusUpdate(quote.id!, status as QuoteRequest['status'])}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="new">New</SelectItem>
                            <SelectItem value="reviewed">Reviewed</SelectItem>
                            <SelectItem value="quoted">Quoted</SelectItem>
                            <SelectItem value="converted">Converted</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="space-y-6">
                    {/* Contact Info */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <a href={`mailto:${quote.email}`} className="text-primary hover:underline">
                          {quote.email}
                        </a>
                      </div>
                      {quote.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <a href={`tel:${quote.phone}`} className="text-primary hover:underline">
                            {quote.phone}
                          </a>
                        </div>
                      )}
                      {quote.best_time_to_call && (
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <span className="text-sm">{quote.best_time_to_call}</span>
                        </div>
                      )}
                    </div>

                    {/* Project Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">Project Type</h4>
                        <p className="text-sm">{quote.project_type}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">Industry</h4>
                        <p className="text-sm">{quote.industry}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">Timeline</h4>
                        <p className="text-sm">{quote.timeline}</p>
                      </div>
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">Budget</h4>
                        <p className="text-sm">{quote.budget}</p>
                      </div>
                    </div>

                    {/* Features */}
                    {quote.key_features && quote.key_features.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">Key Features</h4>
                        <div className="flex flex-wrap gap-2">
                          {quote.key_features.map((feature, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {feature}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Additional Info */}
                    {quote.additional_info && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">Additional Information</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{quote.additional_info}</p>
                      </div>
                    )}

                    {/* Account Status */}
                    {quote.wants_account && (
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                        <p className="text-sm text-blue-700 dark:text-blue-300">
                          ✨ This client requested a blog account
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </ProtectedRoute>
  )
}

export default QuoteRequests
