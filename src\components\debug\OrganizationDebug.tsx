import React from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useOrganization } from '../../contexts/OrganizationContext'

export const OrganizationDebug: React.FC = () => {
  const { user } = useAuth()
  const { 
    currentOrganization, 
    organizations, 
    loading, 
    error 
  } = useOrganization()

  if (!user) return null

  return (
    <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-lg max-w-md text-xs">
      <h3 className="font-semibold mb-2">Organization Debug</h3>
      
      <div className="space-y-2">
        <div>
          <strong>User ID:</strong> {user.id}
        </div>
        
        <div>
          <strong>Loading:</strong> {loading ? 'Yes' : 'No'}
        </div>
        
        <div>
          <strong>Error:</strong> {error || 'None'}
        </div>
        
        <div>
          <strong>Organizations Count:</strong> {organizations.length}
        </div>
        
        <div>
          <strong>Current Org:</strong> {currentOrganization ? currentOrganization.name : 'None'}
        </div>
        
        {organizations.length > 0 && (
          <div>
            <strong>All Organizations:</strong>
            <ul className="ml-2 mt-1">
              {organizations.map(org => (
                <li key={org.id}>
                  {org.name} ({org.slug})
                </li>
              ))}
            </ul>
          </div>
        )}
        
        {currentOrganization && (
          <div>
            <strong>Current Org Details:</strong>
            <div className="ml-2 mt-1">
              <div>Plan: {currentOrganization.subscription_plan}</div>
              <div>Workflow Credits: {currentOrganization.workflow_credits_used}/{currentOrganization.workflow_credits_limit}</div>
              <div>AI Credits: {currentOrganization.ai_credits_used}/{currentOrganization.ai_credits_limit}</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
