import React, { useState, useCallback, useRef } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Trash2, 
  Zap, 
  Settings, 
  Clock, 
  Webhook,
  Mail,
  Code,
  TestTube,
  Save,
  Play,
  ArrowRight,
  Filter,
  Send,
  Database,
  Slack,
  Globe
} from 'lucide-react'
import { toast } from 'sonner'

interface WorkflowNode {
  id: string
  type: 'trigger' | 'condition' | 'action'
  position: { x: number; y: number }
  data: any
  connections: string[]
}

interface VisualWorkflowBuilderProps {
  isOpen: boolean
  onClose: () => void
  editingRule?: any
}

const VisualWorkflowBuilder: React.FC<VisualWorkflowBuilderProps> = ({ 
  isOpen, 
  onClose, 
  editingRule 
}) => {
  const [workflowName, setWorkflowName] = useState(editingRule?.name || '')
  const [workflowDescription, setWorkflowDescription] = useState(editingRule?.description || '')
  const [nodes, setNodes] = useState<WorkflowNode[]>([
    {
      id: 'trigger-1',
      type: 'trigger',
      position: { x: 50, y: 100 },
      data: { triggerType: 'new_quote', label: 'New Quote Received' },
      connections: []
    }
  ])
  const [selectedNode, setSelectedNode] = useState<string | null>(null)
  const [draggedNode, setDraggedNode] = useState<string | null>(null)
  const [isTestMode, setIsTestMode] = useState(false)
  const canvasRef = useRef<HTMLDivElement>(null)

  const nodeTypes = {
    trigger: {
      icon: Zap,
      color: 'bg-blue-500',
      options: [
        { value: 'new_quote', label: 'New Quote Received', icon: Zap },
        { value: 'newsletter_signup', label: 'Newsletter Signup', icon: Mail },
        { value: 'status_change', label: 'Quote Status Changed', icon: Settings },
        { value: 'time_delay', label: 'Time Delay', icon: Clock }
      ]
    },
    condition: {
      icon: Filter,
      color: 'bg-yellow-500',
      options: [
        { value: 'budget_check', label: 'Budget Amount', icon: Filter },
        { value: 'industry_check', label: 'Industry Type', icon: Filter },
        { value: 'project_type', label: 'Project Type', icon: Filter }
      ]
    },
    action: {
      icon: Send,
      color: 'bg-green-500',
      options: [
        { value: 'send_email', label: 'Send Email', icon: Mail },
        { value: 'delay', label: 'Delay/Wait', icon: Clock },
        { value: 'webhook', label: 'Send Webhook', icon: Webhook },
        { value: 'slack', label: 'Slack Notification', icon: Slack },
        { value: 'ghl_sync', label: 'GoHighLevel Sync', icon: Database },
        { value: 'custom_code', label: 'Custom Code', icon: Code }
      ]
    }
  }

  const addNode = (type: 'trigger' | 'condition' | 'action') => {
    const newNode: WorkflowNode = {
      id: `${type}-${Date.now()}`,
      type,
      position: { x: 200 + nodes.length * 50, y: 100 + nodes.length * 20 },
      data: { 
        label: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
        ...(type === 'trigger' && { triggerType: 'new_quote' }),
        ...(type === 'condition' && { field: 'budget', operator: 'contains', value: '' }),
        ...(type === 'action' && { actionType: 'webhook', config: {} })
      },
      connections: []
    }
    setNodes([...nodes, newNode])
  }

  const deleteNode = (nodeId: string) => {
    setNodes(nodes.filter(node => {
      if (node.id === nodeId) return false
      // Remove connections to deleted node
      node.connections = node.connections.filter(conn => conn !== nodeId)
      return true
    }))
    if (selectedNode === nodeId) setSelectedNode(null)
  }

  const updateNodeData = (nodeId: string, data: any) => {
    setNodes(nodes.map(node => 
      node.id === nodeId ? { ...node, data: { ...node.data, ...data } } : node
    ))
  }

  const connectNodes = (fromId: string, toId: string) => {
    setNodes(nodes.map(node => 
      node.id === fromId 
        ? { ...node, connections: [...node.connections.filter(c => c !== toId), toId] }
        : node
    ))
  }

  const renderNode = (node: WorkflowNode) => {
    const nodeType = nodeTypes[node.type]
    const Icon = nodeType.icon
    const isSelected = selectedNode === node.id

    return (
      <div
        key={node.id}
        className={`absolute cursor-pointer transition-all duration-200 ${
          isSelected ? 'scale-105 z-20' : 'z-10'
        }`}
        style={{ 
          left: node.position.x, 
          top: node.position.y,
          transform: isSelected ? 'scale(1.05)' : 'scale(1)'
        }}
        onClick={() => setSelectedNode(node.id)}
      >
        <Card className={`w-48 ${isSelected ? 'ring-2 ring-primary shadow-lg' : 'shadow-md'} hover:shadow-lg transition-shadow`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-3 mb-2">
              <div className={`w-8 h-8 rounded-full ${nodeType.color} flex items-center justify-center`}>
                <Icon className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <Badge variant="outline" className="text-xs">
                  {node.type}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                onClick={(e) => {
                  e.stopPropagation()
                  deleteNode(node.id)
                }}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
            
            <h4 className="font-medium text-sm mb-1 truncate">
              {node.data.label}
            </h4>
            
            {node.type === 'trigger' && (
              <p className="text-xs text-gray-500">
                {nodeTypes.trigger.options.find(o => o.value === node.data.triggerType)?.label}
              </p>
            )}
            
            {node.type === 'condition' && (
              <p className="text-xs text-gray-500">
                {node.data.field} {node.data.operator} {node.data.value}
              </p>
            )}
            
            {node.type === 'action' && (
              <p className="text-xs text-gray-500">
                {nodeTypes.action.options.find(o => o.value === node.data.actionType)?.label}
              </p>
            )}

            {/* Connection points */}
            {node.type !== 'action' && (
              <div className="absolute -right-2 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded-full"></div>
              </div>
            )}
            
            {node.type !== 'trigger' && (
              <div className="absolute -left-2 top-1/2 transform -translate-y-1/2">
                <div className="w-4 h-4 bg-white border-2 border-gray-300 rounded-full"></div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderConnections = () => {
    return nodes.map(node => 
      node.connections.map(targetId => {
        const targetNode = nodes.find(n => n.id === targetId)
        if (!targetNode) return null

        const startX = node.position.x + 192 // node width
        const startY = node.position.y + 40  // node height / 2
        const endX = targetNode.position.x
        const endY = targetNode.position.y + 40

        return (
          <svg
            key={`${node.id}-${targetId}`}
            className="absolute inset-0 pointer-events-none z-5"
            style={{ width: '100%', height: '100%' }}
          >
            <defs>
              <marker
                id="arrowhead"
                markerWidth="10"
                markerHeight="7"
                refX="9"
                refY="3.5"
                orient="auto"
              >
                <polygon
                  points="0 0, 10 3.5, 0 7"
                  fill="#6b7280"
                />
              </marker>
            </defs>
            <path
              d={`M ${startX} ${startY} Q ${startX + 50} ${startY} ${endX} ${endY}`}
              stroke="#6b7280"
              strokeWidth="2"
              fill="none"
              markerEnd="url(#arrowhead)"
            />
          </svg>
        )
      })
    )
  }

  const handleSave = async () => {
    if (!workflowName.trim()) {
      toast.error('Please enter a workflow name')
      return
    }

    // TODO: Save workflow to database
    toast.success('Workflow saved successfully!')
    onClose()
  }

  const handleTest = async () => {
    setIsTestMode(true)
    // TODO: Test workflow execution
    setTimeout(() => {
      setIsTestMode(false)
      toast.success('Workflow test completed!')
    }, 2000)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl font-montserrat">
                {editingRule ? 'Edit Workflow' : 'Create New Workflow'}
              </DialogTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Build your automation workflow visually
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleTest} disabled={isTestMode}>
                {isTestMode ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                Test Workflow
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Workflow
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex h-[calc(95vh-120px)]">
          {/* Sidebar */}
          <div className="w-80 border-r bg-gray-50 dark:bg-gray-900 p-4 overflow-y-auto">
            <div className="space-y-6">
              {/* Basic Info */}
              <div>
                <h3 className="font-semibold mb-3">Workflow Details</h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs">Name</Label>
                    <Input
                      value={workflowName}
                      onChange={(e) => setWorkflowName(e.target.value)}
                      placeholder="Enter workflow name"
                      className="text-sm"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Description</Label>
                    <Textarea
                      value={workflowDescription}
                      onChange={(e) => setWorkflowDescription(e.target.value)}
                      placeholder="Describe what this workflow does"
                      rows={2}
                      className="text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Add Nodes */}
              <div>
                <h3 className="font-semibold mb-3">Add Components</h3>
                <div className="space-y-2">
                  {Object.entries(nodeTypes).map(([type, config]) => {
                    const Icon = config.icon
                    return (
                      <Button
                        key={type}
                        variant="outline"
                        className="w-full justify-start"
                        onClick={() => addNode(type as any)}
                      >
                        <div className={`w-6 h-6 rounded ${config.color} flex items-center justify-center mr-3`}>
                          <Icon className="h-3 w-3 text-white" />
                        </div>
                        Add {type.charAt(0).toUpperCase() + type.slice(1)}
                      </Button>
                    )
                  })}
                </div>
              </div>

              {/* Node Properties */}
              {selectedNode && (
                <div>
                  <h3 className="font-semibold mb-3">Properties</h3>
                  <NodePropertiesPanel
                    node={nodes.find(n => n.id === selectedNode)!}
                    onUpdate={(data) => updateNodeData(selectedNode, data)}
                    nodeTypes={nodeTypes}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Canvas */}
          <div className="flex-1 relative bg-white dark:bg-gray-800 overflow-hidden">
            <div
              ref={canvasRef}
              className="w-full h-full relative"
              style={{ 
                backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
                backgroundSize: '20px 20px'
              }}
            >
              {renderConnections()}
              {nodes.map(renderNode)}
              
              {nodes.length === 1 && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="text-center text-gray-500">
                    <ArrowRight className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">Add components to build your workflow</p>
                    <p className="text-sm">Use the sidebar to add triggers, conditions, and actions</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Separate component for node properties
const NodePropertiesPanel: React.FC<{
  node: WorkflowNode
  onUpdate: (data: any) => void
  nodeTypes: any
}> = ({ node, onUpdate, nodeTypes }) => {
  if (node.type === 'trigger') {
    return (
      <div className="space-y-3">
        <div>
          <Label className="text-xs">Trigger Type</Label>
          <Select
            value={node.data.triggerType}
            onValueChange={(value) => onUpdate({ triggerType: value })}
          >
            <SelectTrigger className="text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {nodeTypes.trigger.options.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    )
  }

  if (node.type === 'condition') {
    return (
      <div className="space-y-3">
        <div>
          <Label className="text-xs">Field</Label>
          <Select
            value={node.data.field}
            onValueChange={(value) => onUpdate({ field: value })}
          >
            <SelectTrigger className="text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="budget">Budget</SelectItem>
              <SelectItem value="industry">Industry</SelectItem>
              <SelectItem value="project_type">Project Type</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="text-xs">Operator</Label>
          <Select
            value={node.data.operator}
            onValueChange={(value) => onUpdate({ operator: value })}
          >
            <SelectTrigger className="text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="contains">Contains</SelectItem>
              <SelectItem value="equals">Equals</SelectItem>
              <SelectItem value="greater_than">Greater Than</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label className="text-xs">Value</Label>
          <Input
            value={node.data.value}
            onChange={(e) => onUpdate({ value: e.target.value })}
            placeholder="Enter value"
            className="text-sm"
          />
        </div>
      </div>
    )
  }

  if (node.type === 'action') {
    return (
      <div className="space-y-3">
        <div>
          <Label className="text-xs">Action Type</Label>
          <Select
            value={node.data.actionType}
            onValueChange={(value) => onUpdate({ actionType: value })}
          >
            <SelectTrigger className="text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {nodeTypes.action.options.map((option: any) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {node.data.actionType === 'webhook' && (
          <div>
            <Label className="text-xs">Webhook URL</Label>
            <Input
              value={node.data.config?.url || ''}
              onChange={(e) => onUpdate({
                config: { ...node.data.config, url: e.target.value }
              })}
              placeholder="https://hooks.slack.com/..."
              className="text-sm font-mono"
            />
          </div>
        )}

        {(node.data.actionType === 'send_email' || node.data.actionType === 'email') && (
          <>
            <div>
              <Label className="text-xs">To Email</Label>
              <Input
                value={node.data.config?.to || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, to: e.target.value }
                })}
                placeholder="{{ email }} or <EMAIL>"
                className="text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Subject</Label>
              <Input
                value={node.data.config?.subject || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, subject: e.target.value }
                })}
                placeholder="Welcome {{ name }}!"
                className="text-sm"
              />
            </div>
            <div>
              <Label className="text-xs">Body</Label>
              <textarea
                value={node.data.config?.body || ''}
                onChange={(e) => onUpdate({
                  config: { ...node.data.config, body: e.target.value }
                })}
                placeholder="Email content with {{ variables }}"
                className="w-full text-sm border rounded px-2 py-1 min-h-[60px] resize-none"
                rows={3}
              />
            </div>
          </>
        )}

        {node.data.actionType === 'delay' && (
          <div>
            <Label className="text-xs">Delay</Label>
            <div className="grid grid-cols-2 gap-1">
              <Input
                type="number"
                value={node.data.config?.delayValue || ''}
                onChange={(e) => {
                  const value = parseInt(e.target.value) || 0
                  const unit = node.data.config?.delayUnit || 'minutes'
                  const delayMs = convertToMilliseconds(value, unit)
                  onUpdate({
                    config: {
                      ...node.data.config,
                      delayValue: value,
                      delayUnit: unit,
                      delayMs
                    }
                  })
                }}
                placeholder="5"
                min="1"
                className="text-sm"
              />
              <Select
                value={node.data.config?.delayUnit || 'minutes'}
                onValueChange={(unit) => {
                  const value = parseInt(node.data.config?.delayValue) || 0
                  const delayMs = convertToMilliseconds(value, unit)
                  onUpdate({
                    config: {
                      ...node.data.config,
                      delayValue: value,
                      delayUnit: unit,
                      delayMs
                    }
                  })
                }}
              >
                <SelectTrigger className="text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="seconds">Sec</SelectItem>
                  <SelectItem value="minutes">Min</SelectItem>
                  <SelectItem value="hours">Hr</SelectItem>
                  <SelectItem value="days">Day</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
      </div>
    )
  }

  return null
}

// Helper function to convert delay to milliseconds
function convertToMilliseconds(value: number, unit: string): number {
  switch (unit) {
    case 'seconds': return value * 1000
    case 'minutes': return value * 60 * 1000
    case 'hours': return value * 60 * 60 * 1000
    case 'days': return value * 24 * 60 * 60 * 1000
    default: return value * 60 * 1000 // default to minutes
  }
}

export default VisualWorkflowBuilder
