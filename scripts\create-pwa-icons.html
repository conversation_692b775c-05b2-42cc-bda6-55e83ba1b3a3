<!DOCTYPE html>
<html>
<head>
    <title>PWA Icon Generator</title>
</head>
<body>
    <h1>PWA Icon Generator</h1>
    <p>This will create PNG icons from the SVG files</p>
    
    <canvas id="canvas192" width="192" height="192" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    <canvas id="canvas512" width="512" height="512" style="border: 1px solid #ccc; margin: 10px;"></canvas>
    
    <div>
        <button onclick="downloadIcon(192)">Download 192x192</button>
        <button onclick="downloadIcon(512)">Download 512x512</button>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Fill background with brand color
            ctx.fillStyle = '#06b6d4';
            ctx.fillRect(0, 0, size, size);
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size/8}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('MBI', size/2, size/2);
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `pwa-${size}x${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Create icons on load
        createIcon(192);
        createIcon(512);
    </script>
</body>
</html>
