import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { SMTPClient } from "https://deno.land/x/denomailer@1.6.0/mod.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
}

interface EmailRequest {
  integrationId: string
  to: string
  subject: string
  body: string
  testEmail?: boolean
}

interface EmailIntegration {
  id: string
  provider: string
  config: any
  user_id: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }

  try {
    // Parse the request body
    const requestBody: EmailRequest = await req.json()
    console.log('Email request received:', { 
      integrationId: requestBody.integrationId, 
      to: requestBody.to, 
      subject: requestBody.subject 
    })

    const { integrationId, to, subject, body, testEmail } = requestBody

    // Validate required fields
    if (!integrationId || !to || !subject || !body) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields', 
          required: ['integrationId', 'to', 'subject', 'body']
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Get the email integration
    const { data: integration, error: integrationError } = await supabase
      .from('email_integrations')
      .select('*')
      .eq('id', integrationId)
      .single()

    if (integrationError || !integration) {
      return new Response(
        JSON.stringify({ 
          error: 'Email integration not found',
          integrationId 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Send email based on provider
    let result
    switch (integration.provider) {
      case 'gmail':
        result = await sendGmailEmail(integration, { to, subject, body })
        break
      case 'outlook':
        result = await sendOutlookEmail(integration, { to, subject, body })
        break
      case 'smtp':
        result = await sendSMTPEmail(integration, { to, subject, body })
        break
      case 'resend':
        result = await sendResendEmail(integration, { to, subject, body })
        break
      default:
        return new Response(
          JSON.stringify({
            error: 'Unsupported provider for server-side sending',
            provider: integration.provider,
            message: 'This provider should be handled client-side'
          }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        )
    }

    // Log the email attempt
    try {
      await supabase.from('workflow_executions').insert({
        workflow_id: integration.id,
        workflow_name: `Email: ${integration.name}`,
        trigger_data: { to, subject, testEmail },
        status: result.success ? 'success' : 'failed',
        error_message: result.error || null,
        execution_time_ms: result.executionTime || 0
      })
    } catch (logError) {
      console.error('Failed to log email execution:', logError)
    }

    return new Response(
      JSON.stringify({
        success: result.success,
        message: result.success ? 'Email sent successfully' : result.error,
        messageId: result.messageId,
        provider: integration.provider
      }),
      {
        status: result.success ? 200 : 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )

  } catch (error) {
    console.error('Email sending error:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

async function sendGmailEmail(integration: EmailIntegration, message: { to: string, subject: string, body: string }) {
  const startTime = Date.now()

  try {
    const config = integration.config
    console.log('Gmail config check:', {
      hasEmail: !!config.email,
      hasAppPassword: !!config.appPassword,
      emailLength: config.email?.length,
      passwordLength: config.appPassword?.length,
      emailValue: config.email, // Log actual email for debugging
      passwordPrefix: config.appPassword?.substring(0, 4) + '...' // Log first 4 chars of password
    })

    // Validate configuration
    if (!config.email || !config.appPassword) {
      return {
        success: false,
        error: 'Gmail configuration missing: email or appPassword not provided',
        executionTime: Date.now() - startTime
      }
    }

    // Additional validation
    if (!config.email.includes('@gmail.com')) {
      return {
        success: false,
        error: `Gmail configuration error: email must be a @gmail.com address. Got: ${config.email}`,
        executionTime: Date.now() - startTime
      }
    }

    if (config.appPassword.length !== 16) {
      return {
        success: false,
        error: `Gmail configuration error: app password must be 16 characters, got ${config.appPassword.length}. Password starts with: ${config.appPassword.substring(0, 4)}...`,
        executionTime: Date.now() - startTime
      }
    }

    // Try Gmail API approach first, fallback to SMTP
    try {
      console.log('Attempting Gmail via SMTP with enhanced config...')

      const client = new SMTPClient({
        connection: {
          hostname: "smtp.gmail.com",
          port: 465, // Try SSL port instead of TLS
          tls: true,
          auth: {
            username: config.email,
            password: config.appPassword,
          },
        },
      })

      // Format the from address with name if provided
      const fromAddress = config.fromName
        ? `${config.fromName} <${config.email}>`
        : config.email

      await client.send({
        from: fromAddress,
        to: message.to,
        subject: message.subject,
        content: message.body,
        html: message.body,
      })

      console.log('Gmail email sent successfully via SSL')
      await client.close()
    } catch (sslError) {
      console.log('SSL failed, trying STARTTLS...', sslError.message)

      // Fallback to STARTTLS
      const client = new SMTPClient({
        connection: {
          hostname: "smtp.gmail.com",
          port: 587,
          tls: false, // Start without TLS, then upgrade
          auth: {
            username: config.email,
            password: config.appPassword,
          },
        },
      })

      // Format the from address with name if provided
      const fromAddress = config.fromName
        ? `${config.fromName} <${config.email}>`
        : config.email

      await client.send({
        from: fromAddress,
        to: message.to,
        subject: message.subject,
        content: message.body,
        html: message.body,
      })

      console.log('Gmail email sent successfully via STARTTLS')
      await client.close()
    }

    return {
      success: true,
      messageId: `gmail_${Date.now()}`,
      executionTime: Date.now() - startTime
    }
  } catch (error) {
    console.error('Gmail SMTP error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    })

    // Provide more specific error messages
    let errorMessage = error.message
    if (error.message.includes('InvalidContentType')) {
      errorMessage = 'Gmail authentication failed. Please check your app password is correct and 2FA is enabled.'
    } else if (error.message.includes('authentication')) {
      errorMessage = 'Gmail authentication failed. Verify your email and app password.'
    } else if (error.message.includes('connection')) {
      errorMessage = 'Failed to connect to Gmail SMTP server. Check your internet connection.'
    }

    return {
      success: false,
      error: `Gmail SMTP error: ${errorMessage}`,
      executionTime: Date.now() - startTime
    }
  }
}

async function sendOutlookEmail(integration: EmailIntegration, message: { to: string, subject: string, body: string }) {
  const startTime = Date.now()
  
  try {
    const config = integration.config
    
    const client = new SMTPClient({
      connection: {
        hostname: "smtp-mail.outlook.com",
        port: 587,
        tls: true,
        auth: {
          username: config.email,
          password: config.password,
        },
      },
    })

    await client.send({
      from: config.email,
      to: message.to,
      subject: message.subject,
      content: message.body,
      html: message.body,
    })

    await client.close()

    return {
      success: true,
      messageId: `outlook_${Date.now()}`,
      executionTime: Date.now() - startTime
    }
  } catch (error) {
    console.error('Outlook SMTP error:', error)
    return {
      success: false,
      error: `Outlook SMTP error: ${error.message}`,
      executionTime: Date.now() - startTime
    }
  }
}

async function sendSMTPEmail(integration: EmailIntegration, message: { to: string, subject: string, body: string }) {
  const startTime = Date.now()

  try {
    const config = integration.config

    const client = new SMTPClient({
      connection: {
        hostname: config.host,
        port: parseInt(config.port) || 587,
        tls: config.tls !== false, // Default to true unless explicitly false
        auth: {
          username: config.username,
          password: config.password,
        },
      },
    })

    await client.send({
      from: config.fromEmail || config.username,
      to: message.to,
      subject: message.subject,
      content: message.body,
      html: message.body,
    })

    await client.close()

    return {
      success: true,
      messageId: `smtp_${Date.now()}`,
      executionTime: Date.now() - startTime
    }
  } catch (error) {
    console.error('SMTP error:', error)
    return {
      success: false,
      error: `SMTP error: ${error.message}`,
      executionTime: Date.now() - startTime
    }
  }
}

async function sendResendEmail(integration: EmailIntegration, message: { to: string, subject: string, body: string }) {
  const startTime = Date.now()

  try {
    const config = integration.config

    // Validate Resend configuration
    if (!config.apiKey) {
      return {
        success: false,
        error: 'Resend configuration missing: API key not provided',
        executionTime: Date.now() - startTime
      }
    }

    if (!config.fromEmail) {
      return {
        success: false,
        error: 'Resend configuration missing: from email not provided',
        executionTime: Date.now() - startTime
      }
    }

    console.log('Attempting to send Resend email...')

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: config.fromEmail,
        to: [message.to],
        subject: message.subject,
        html: message.body,
      }),
    })

    const result = await response.json()

    if (!response.ok) {
      console.error('Resend API error:', result)
      return {
        success: false,
        error: `Resend API error: ${result.message || 'Unknown error'}`,
        executionTime: Date.now() - startTime
      }
    }

    console.log('Resend email sent successfully:', result.id)

    return {
      success: true,
      messageId: result.id,
      executionTime: Date.now() - startTime
    }
  } catch (error) {
    console.error('Resend error:', error)
    return {
      success: false,
      error: `Resend error: ${error.message}`,
      executionTime: Date.now() - startTime
    }
  }
}
